import { ITypeSuite, TType } from "./types";
import { IErrorDetail } from "./util";
/**
 * Export functions used to define interfaces.
 */
export { TArray, TEnumType, TEnumLiteral, TFunc, TIface, TLiteral, TName, TOptional, TParam, TParamList, TProp, TTuple, TType, TUnion, TIntersection, array, enumlit, enumtype, func, iface, lit, name, opt, param, tuple, union, intersection, BasicType, ITypeSuite, } from "./types";
export { VError, IErrorDetail } from './util';
export interface ICheckerSuite {
    [name: string]: Checker;
}
/**
 * Takes one of more type suites (e.g. a module generated by `ts-interface-builder`), and combines
 * them into a suite of interface checkers. If a type is used by name, that name should be present
 * among the passed-in type suites.
 *
 * The returned object maps type names to Checker objects.
 */
export declare function createCheckers(...typeSuite: ITypeSuite[]): ICheckerSuite;
/**
 * Checker implements validation of objects, and also includes accessors to validate method calls.
 * Checkers should be created using `createCheckers()`.
 */
export declare class Checker {
    private suite;
    private ttype;
    private _path;
    private props;
    private checkerPlain;
    private checkerStrict;
    constructor(suite: ITypeSuite, ttype: TType, _path?: string);
    /**
     * Set the path to report in errors, instead of the default "value". (E.g. if the Checker is for
     * a "person" interface, set path to "person" to report e.g. "person.name is not a string".)
     */
    setReportedPath(path: string): void;
    /**
     * Check that the given value satisfies this checker's type, or throw Error.
     */
    check(value: any): void;
    /**
     * A fast check for whether or not the given value satisfies this Checker's type. This returns
     * true or false, does not produce an error message, and is fast both on success and on failure.
     */
    test(value: any): boolean;
    /**
     * Returns an error object describing the errors if the given value does not satisfy this
     * Checker's type, or null if it does.
     */
    validate(value: any): IErrorDetail | null;
    /**
     * Check that the given value satisfies this checker's type strictly. This checks that objects
     * and tuples have no extra members. Note that this prevents backward compatibility, so usually
     * a plain check() is more appropriate.
     */
    strictCheck(value: any): void;
    /**
     * A fast strict check for whether or not the given value satisfies this Checker's type. Returns
     * true or false, does not produce an error message, and is fast both on success and on failure.
     */
    strictTest(value: any): boolean;
    /**
     * Returns an error object describing the errors if the given value does not satisfy this
     * Checker's type strictly, or null if it does.
     */
    strictValidate(value: any): IErrorDetail | null;
    /**
     * If this checker is for an interface, returns a Checker for the type required for the given
     * property of this interface.
     */
    getProp(prop: string): Checker;
    /**
     * If this checker is for an interface, returns a Checker for the argument-list required to call
     * the given method of this interface. E.g. if this Checker is for the interface:
     *    interface Foo {
     *      find(s: string, pos?: number): number;
     *    }
     * Then methodArgs("find").check(...) will succeed for ["foo"] and ["foo", 3], but not for [17].
     */
    methodArgs(methodName: string): Checker;
    /**
     * If this checker is for an interface, returns a Checker for the return value of the given
     * method of this interface.
     */
    methodResult(methodName: string): Checker;
    /**
     * If this checker is for a function, returns a Checker for its argument-list.
     */
    getArgs(): Checker;
    /**
     * If this checker is for a function, returns a Checker for its result.
     */
    getResult(): Checker;
    /**
     * Return the type for which this is a checker.
     */
    getType(): TType;
    /**
     * Actual implementation of check() and strictCheck().
     */
    private _doCheck;
    private _doValidate;
    private _getMethod;
}
/**
 * Typed checker interface. Adds type guard functionality to a normal `Checker`.
 *
 * To use, cast a `Checker` to a `CheckerT<>` using the appropriate type.
 *
 * eg.
 *   import { MyInterface } from './my-interface';
 *   import MyInterfaceTi from './my-interface-ti';
 *
 *   const checkers = createCheckers(MyInterfaceTi) as {
 *     MyInterface: CheckerT<MyInterface>
 *   };
 *
 * TODO:
 * - Enable `check()` and `strictCheck()` type assertion definitions once the functionality
 *   is correctly working in TypeScript. (https://github.com/microsoft/TypeScript/issues/36931)
 */
export interface CheckerT<T> extends Checker {
    test(value: any): value is T;
    strictTest(value: any): value is T;
}
