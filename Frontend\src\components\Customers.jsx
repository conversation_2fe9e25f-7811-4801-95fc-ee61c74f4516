import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Eye, Search, Filter } from 'lucide-react';
import Layout from './shared/Layout';
import Table from './shared/Table';
import Modal from './shared/Modal';
import FormInput from './shared/FormInput';
import Button from './shared/Button';

const Customers = () => {
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState('');
  const [sortDirection, setSortDirection] = useState('asc');
  
  // د مودال حالتونه
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  
  // د فورم ډیټا
  const [formData, setFormData] = useState({
    customer_code: '',
    full_name: '',
    mobile_number: '',
    address: '',
    loan_limit: '500000'
  });
  const [formErrors, setFormErrors] = useState({});

  // د نمونه ډیټا
  const sampleCustomers = [
    {
      id: 1,
      customer_code: 'C001',
      full_name: 'احمد علي خان',
      mobile_number: '۰۷۹۹۱۲۳۴۵۶',
      address: 'کابل، افغانستان',
      loan_limit: 500000,
      balance: 25000,
      status: 'فعال',
      created_at: '۱۴۰۲/۰۸/۱۵'
    },
    {
      id: 2,
      customer_code: 'C002',
      full_name: 'فاطمه احمد',
      mobile_number: '۰۷۰۰۹۸۷۶۵۴',
      address: 'هرات، افغانستان',
      loan_limit: 300000,
      balance: -15000,
      status: 'فعال',
      created_at: '۱۴۰۲/۰۸/۱۰'
    },
    {
      id: 3,
      customer_code: 'C003',
      full_name: 'محمد حسن رحیمي',
      mobile_number: '۰۷۸۸۵۵۵۱۲۳',
      address: 'مزار شریف، افغانستان',
      loan_limit: 750000,
      balance: 50000,
      status: 'فعال',
      created_at: '۱۴۰۲/۰۸/۰۵'
    }
  ];

  useEffect(() => {
    loadCustomers();
  }, []);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchTerm]);

  const loadCustomers = () => {
    setLoading(true);
    // د API د اړیکې لپاره وروسته به دا ځای بدل شي
    setTimeout(() => {
      setCustomers(sampleCustomers);
      setLoading(false);
    }, 1000);
  };

  const filterCustomers = () => {
    let filtered = customers;
    
    if (searchTerm) {
      filtered = filtered.filter(customer =>
        customer.full_name.includes(searchTerm) ||
        customer.customer_code.includes(searchTerm) ||
        customer.mobile_number.includes(searchTerm)
      );
    }
    
    setFilteredCustomers(filtered);
  };

  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
    
    const sorted = [...filteredCustomers].sort((a, b) => {
      if (direction === 'asc') {
        return a[field] > b[field] ? 1 : -1;
      } else {
        return a[field] < b[field] ? 1 : -1;
      }
    });
    
    setFilteredCustomers(sorted);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // د غلطۍ پاکول
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.full_name.trim()) {
      errors.full_name = 'د پیرودونکي نوم اړین دی';
    }
    
    if (!formData.mobile_number.trim()) {
      errors.mobile_number = 'د موبایل شمیره اړینه ده';
    }
    
    if (!formData.loan_limit || formData.loan_limit <= 0) {
      errors.loan_limit = 'د پور حد اړین دی';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddCustomer = () => {
    if (!validateForm()) return;
    
    const newCustomer = {
      id: customers.length + 1,
      customer_code: `C${String(customers.length + 1).padStart(3, '0')}`,
      ...formData,
      balance: 0,
      status: 'فعال',
      created_at: new Date().toLocaleDateString('fa-AF')
    };
    
    setCustomers(prev => [...prev, newCustomer]);
    setShowAddModal(false);
    resetForm();
  };

  const handleEditCustomer = () => {
    if (!validateForm()) return;
    
    setCustomers(prev => prev.map(customer =>
      customer.id === selectedCustomer.id
        ? { ...customer, ...formData }
        : customer
    ));
    
    setShowEditModal(false);
    resetForm();
  };

  const handleDeleteCustomer = () => {
    setCustomers(prev => prev.filter(customer => customer.id !== selectedCustomer.id));
    setShowDeleteModal(false);
    setSelectedCustomer(null);
  };

  const resetForm = () => {
    setFormData({
      customer_code: '',
      full_name: '',
      mobile_number: '',
      address: '',
      loan_limit: '500000'
    });
    setFormErrors({});
    setSelectedCustomer(null);
  };

  const openEditModal = (customer) => {
    setSelectedCustomer(customer);
    setFormData({
      customer_code: customer.customer_code,
      full_name: customer.full_name,
      mobile_number: customer.mobile_number,
      address: customer.address,
      loan_limit: customer.loan_limit.toString()
    });
    setShowEditModal(true);
  };

  const openDeleteModal = (customer) => {
    setSelectedCustomer(customer);
    setShowDeleteModal(true);
  };

  const columns = [
    {
      key: 'customer_code',
      title: 'د پیرودونکي کوډ',
      sortable: true,
      render: (value) => <span className="font-mono">{value}</span>
    },
    {
      key: 'full_name',
      title: 'بشپړ نوم',
      sortable: true,
      render: (value) => <span className="pashto-text font-medium">{value}</span>
    },
    {
      key: 'mobile_number',
      title: 'د موبایل شمیره',
      render: (value) => <span className="font-mono">{value}</span>
    },
    {
      key: 'balance',
      title: 'میزان',
      sortable: true,
      render: (value) => (
        <span className={`font-medium ${value >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {value >= 0 ? '+' : ''}{value.toLocaleString()} افغانۍ
        </span>
      )
    },
    {
      key: 'status',
      title: 'حالت',
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
          value === 'فعال' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'actions',
      title: 'کارونه',
      render: (_, customer) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => openEditModal(customer)}
            className="text-blue-600 hover:text-blue-800"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={() => openDeleteModal(customer)}
            className="text-red-600 hover:text-red-800"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  return (
    <Layout title="د پیرودونکو مدیریت">
      <div className="space-y-6">
        {/* د پورته برخه */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 pashto-text">د پیرودونکو لیست</h1>
            <p className="text-gray-600 pashto-text">ټول پیرودونکي: {filteredCustomers.length}</p>
          </div>
          <Button
            onClick={() => setShowAddModal(true)}
            icon={Plus}
            variant="primary"
          >
            نوی پیرودونکی اضافه کړئ
          </Button>
        </div>

        {/* د لټون او فلټر برخه */}
        <div className="bg-white p-4 rounded-lg shadow-md">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="د نوم، کوډ یا موبایل له مخې لټون..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-sarafi-500 focus:border-transparent"
                  dir="rtl"
                />
              </div>
            </div>
            <Button variant="outline" icon={Filter}>
              فلټر
            </Button>
          </div>
        </div>

        {/* د پیرودونکو جدول */}
        <Table
          columns={columns}
          data={filteredCustomers}
          loading={loading}
          onSort={handleSort}
          sortField={sortField}
          sortDirection={sortDirection}
          emptyMessage="هیڅ پیرودونکی نه دی موندل شوی"
        />
      </div>

      {/* د اضافه کولو مودال */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="نوی پیرودونکی اضافه کړئ"
        size="lg"
      >
        <div className="space-y-4">
          <FormInput
            label="بشپړ نوم"
            name="full_name"
            value={formData.full_name}
            onChange={handleInputChange}
            placeholder="د پیرودونکي بشپړ نوم ولیکئ"
            required
            error={formErrors.full_name}
          />
          
          <FormInput
            label="د موبایل شمیره"
            name="mobile_number"
            value={formData.mobile_number}
            onChange={handleInputChange}
            placeholder="۰۷۹۹۱۲۳۴۵۶"
            required
            error={formErrors.mobile_number}
          />
          
          <FormInput
            label="پته"
            name="address"
            type="textarea"
            value={formData.address}
            onChange={handleInputChange}
            placeholder="د پیرودونکي پته ولیکئ"
            rows={3}
          />
          
          <FormInput
            label="د پور حد (افغانۍ)"
            name="loan_limit"
            type="number"
            value={formData.loan_limit}
            onChange={handleInputChange}
            placeholder="500000"
            required
            error={formErrors.loan_limit}
          />
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => setShowAddModal(false)}
            >
              لغوه کول
            </Button>
            <Button
              variant="primary"
              onClick={handleAddCustomer}
            >
              ثبت کول
            </Button>
          </div>
        </div>
      </Modal>

      {/* د تصحیح مودال */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="د پیرودونکي معلومات تصحیح"
        size="lg"
      >
        <div className="space-y-4">
          <FormInput
            label="بشپړ نوم"
            name="full_name"
            value={formData.full_name}
            onChange={handleInputChange}
            placeholder="د پیرودونکي بشپړ نوم ولیکئ"
            required
            error={formErrors.full_name}
          />
          
          <FormInput
            label="د موبایل شمیره"
            name="mobile_number"
            value={formData.mobile_number}
            onChange={handleInputChange}
            placeholder="۰۷۹۹۱۲۳۴۵۶"
            required
            error={formErrors.mobile_number}
          />
          
          <FormInput
            label="پته"
            name="address"
            type="textarea"
            value={formData.address}
            onChange={handleInputChange}
            placeholder="د پیرودونکي پته ولیکئ"
            rows={3}
          />
          
          <FormInput
            label="د پور حد (افغانۍ)"
            name="loan_limit"
            type="number"
            value={formData.loan_limit}
            onChange={handleInputChange}
            placeholder="500000"
            required
            error={formErrors.loan_limit}
          />
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => setShowEditModal(false)}
            >
              لغوه کول
            </Button>
            <Button
              variant="primary"
              onClick={handleEditCustomer}
            >
              تصحیح کول
            </Button>
          </div>
        </div>
      </Modal>

      {/* د ړنګولو مودال */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="د پیرودونکي ړنګول"
        size="md"
      >
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <Trash2 className="h-6 w-6 text-red-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 pashto-text">
            ایا تاسو ډاډه یاست؟
          </h3>
          <p className="text-sm text-gray-500 mb-6 pashto-text">
            دا عمل د "{selectedCustomer?.full_name}" پیرودونکی به د تل لپاره ړنګ کړي. دا عمل بیرته نشي کیدی.
          </p>
          <div className="flex justify-center space-x-3">
            <Button
              variant="secondary"
              onClick={() => setShowDeleteModal(false)}
            >
              لغوه کول
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteCustomer}
            >
              ړنګول
            </Button>
          </div>
        </div>
      </Modal>
    </Layout>
  );
};

export default Customers;
