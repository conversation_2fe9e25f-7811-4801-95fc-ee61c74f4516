import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Download,
  Printer,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  User,
} from "lucide-react";
import Layout from "./shared/Layout";
import FormInput from "./shared/FormInput";
import Button from "./shared/Button";

const Ledger = () => {
  const [selectedCustomer, setSelectedCustomer] = useState("");
  const [transactions, setTransactions] = useState([]);
  const [filteredTransactions, setFilteredTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    from: "",
    to: "",
  });
  const [customerBalance, setCustomerBalance] = useState({
    total_jamah: 0,
    total_naam: 0,
    current_balance: 0,
  });

  // د پیرودونکو لیست
  const customers = [
    { value: "1", label: "احمد علي خان" },
    { value: "2", label: "فاطمه احمد" },
    { value: "3", label: "محمد حسن رحیمي" },
    { value: "4", label: "عایشه خان" },
    { value: "5", label: "علي رضا احمدي" },
  ];

  // د نمونه راکړو ورکړو ډیټا
  const sampleTransactions = [
    {
      id: 1,
      date: "۱۴۰۲/۰۸/۱۵",
      time: "۱۰:۳۰",
      type: "جمعه",
      amount: 50000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      balance_after: 50000,
      daybook_page: 15,
      reference: "DB-15-001",
    },
    {
      id: 2,
      date: "۱۴۰۲/۰۸/۱۶",
      time: "۱۱:۱۵",
      type: "نام",
      amount: 25000,
      currency: "افغانۍ",
      description: "د پیسو ورکول",
      balance_after: 25000,
      daybook_page: 16,
      reference: "DB-16-003",
    },
    {
      id: 3,
      date: "۱۴۰۲/۰۸/۱۷",
      time: "۰۹:۴۵",
      type: "جمعه",
      amount: 75000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      balance_after: 100000,
      daybook_page: 17,
      reference: "DB-17-002",
    },
    {
      id: 4,
      date: "۱۴۰۲/۰۸/۱۸",
      time: "۱۴:۲۰",
      type: "نام",
      amount: 30000,
      currency: "افغانۍ",
      description: "د پیسو ورکول",
      balance_after: 70000,
      daybook_page: 18,
      reference: "DB-18-001",
    },
    {
      id: 5,
      date: "۱۴۰۲/۰۸/۱۹",
      time: "۱۶:۱۰",
      type: "جمعه",
      amount: 40000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      balance_after: 110000,
      daybook_page: 19,
      reference: "DB-19-004",
    },
  ];

  useEffect(() => {
    if (selectedCustomer) {
      loadCustomerTransactions();
    }
  }, [selectedCustomer]);

  useEffect(() => {
    filterTransactions();
  }, [transactions, dateRange]);

  const loadCustomerTransactions = () => {
    setLoading(true);
    // د API د اړیکې لپاره وروسته به دا ځای بدل شي
    setTimeout(() => {
      setTransactions(sampleTransactions);
      calculateBalance(sampleTransactions);
      setLoading(false);
    }, 1000);
  };

  const calculateBalance = (transactionList) => {
    const totalJamah = transactionList
      .filter((t) => t.type === "جمعه")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalNaam = transactionList
      .filter((t) => t.type === "نام")
      .reduce((sum, t) => sum + t.amount, 0);

    setCustomerBalance({
      total_jamah: totalJamah,
      total_naam: totalNaam,
      current_balance: totalJamah - totalNaam,
    });
  };

  const filterTransactions = () => {
    let filtered = transactions;

    if (dateRange.from && dateRange.to) {
      // د نیټې د فلټر کولو لپاره
      filtered = filtered.filter((transaction) => {
        // دلته د نیټې د پرتلې منطق اضافه کړئ
        return true;
      });
    }

    setFilteredTransactions(filtered);
  };

  const handleCustomerChange = (e) => {
    setSelectedCustomer(e.target.value);
    if (!e.target.value) {
      setTransactions([]);
      setFilteredTransactions([]);
      setCustomerBalance({
        total_jamah: 0,
        total_naam: 0,
        current_balance: 0,
      });
    }
  };

  const handleDateRangeChange = (e) => {
    const { name, value } = e.target;
    setDateRange((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    // د ایکسپورټ منطق
    console.log("Exporting ledger data...");
  };

  const selectedCustomerName =
    customers.find((c) => c.value === selectedCustomer)?.label || "";

  return (
    <Layout title='د پیرودونکي کاتا'>
      <div className='space-y-6'>
        {/* د فلټر برخه */}
        <div className='bg-white rounded-lg shadow-md p-6'>
          <h2 className='text-lg font-semibold text-gray-900 mb-4 pashto-text'>
            د پیرودونکي کاتا کتنه
          </h2>

          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <FormInput
              label='پیرودونکی انتخاب کړئ'
              name='customer'
              type='select'
              value={selectedCustomer}
              onChange={handleCustomerChange}
              options={customers}
              placeholder='پیرودونکی انتخاب کړئ'
              icon={User}
            />

            <FormInput
              label='د نیټې څخه'
              name='from'
              type='date'
              value={dateRange.from}
              onChange={handleDateRangeChange}
              icon={Calendar}
            />

            <FormInput
              label='د نیټې پورې'
              name='to'
              type='date'
              value={dateRange.to}
              onChange={handleDateRangeChange}
              icon={Calendar}
            />

            <div className='flex items-end space-x-2'>
              <Button
                variant='outline'
                icon={Filter}
                onClick={filterTransactions}
              >
                فلټر
              </Button>

              {selectedCustomer && (
                <>
                  <Button
                    variant='outline'
                    icon={Printer}
                    onClick={handlePrint}
                  >
                    چاپ
                  </Button>

                  <Button
                    variant='outline'
                    icon={Download}
                    onClick={handleExport}
                  >
                    ایکسپورټ
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* د پیرودونکي معلومات */}
        {selectedCustomer && (
          <div className='bg-white rounded-lg shadow-md p-6'>
            <div className='flex items-center justify-between mb-6'>
              <div>
                <h3 className='text-xl font-bold text-gray-900 pashto-text'>
                  د {selectedCustomerName} کاتا
                </h3>
                <p className='text-gray-600 pashto-text'>
                  ټولې راکړې ورکړې: {filteredTransactions.length}
                </p>
              </div>

              <div className='text-left'>
                <p
                  className={`text-2xl font-bold ${
                    customerBalance.current_balance >= 0
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {customerBalance.current_balance >= 0 ? "+" : ""}
                  {customerBalance.current_balance.toLocaleString()} افغانۍ
                </p>
                <p className='text-sm text-gray-500 pashto-text'>اوسنی میزان</p>
              </div>
            </div>

            {/* د میزان لنډیز */}
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>
              <div className='bg-green-50 p-4 rounded-lg'>
                <div className='flex items-center'>
                  <ArrowUpRight className='h-8 w-8 text-green-600 ml-3' />
                  <div>
                    <p className='text-sm text-green-600 pashto-text'>
                      ټوله جمعه
                    </p>
                    <p className='text-xl font-bold text-green-700'>
                      {customerBalance.total_jamah.toLocaleString()} افغانۍ
                    </p>
                  </div>
                </div>
              </div>

              <div className='bg-red-50 p-4 rounded-lg'>
                <div className='flex items-center'>
                  <ArrowDownRight className='h-8 w-8 text-red-600 ml-3' />
                  <div>
                    <p className='text-sm text-red-600 pashto-text'>ټول نام</p>
                    <p className='text-xl font-bold text-red-700'>
                      {customerBalance.total_naam.toLocaleString()} افغانۍ
                    </p>
                  </div>
                </div>
              </div>

              <div className='bg-blue-50 p-4 rounded-lg'>
                <div className='flex items-center'>
                  <div
                    className={`h-8 w-8 rounded-full flex items-center justify-center ml-3 ${
                      customerBalance.current_balance >= 0
                        ? "bg-green-600"
                        : "bg-red-600"
                    }`}
                  >
                    <span className='text-white font-bold'>
                      {customerBalance.current_balance >= 0 ? "+" : "-"}
                    </span>
                  </div>
                  <div>
                    <p className='text-sm text-blue-600 pashto-text'>
                      نهایي میزان
                    </p>
                    <p
                      className={`text-xl font-bold ${
                        customerBalance.current_balance >= 0
                          ? "text-green-700"
                          : "text-red-700"
                      }`}
                    >
                      {Math.abs(
                        customerBalance.current_balance
                      ).toLocaleString()}{" "}
                      افغانۍ
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* د راکړو ورکړو جدول */}
        {selectedCustomer && (
          <div className='bg-white rounded-lg shadow-md overflow-hidden'>
            <div className='px-6 py-4 border-b border-gray-200'>
              <h4 className='text-lg font-semibold text-gray-900 pashto-text'>
                د راکړو ورکړو تفصیل
              </h4>
            </div>

            {loading ? (
              <div className='p-8 text-center'>
                <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-sarafi-600 mx-auto'></div>
                <p className='mt-2 text-gray-500 pashto-text'>
                  د معلوماتو بارول...
                </p>
              </div>
            ) : (
              <div className='overflow-x-auto'>
                <table className='min-w-full divide-y divide-gray-200'>
                  <thead className='bg-gray-50'>
                    <tr>
                      <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                        نیټه
                      </th>
                      <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                        تفصیل
                      </th>
                      <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                        جمعه
                      </th>
                      <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                        نام
                      </th>
                      <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                        میزان
                      </th>
                      <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                        مرجع
                      </th>
                    </tr>
                  </thead>
                  <tbody className='bg-white divide-y divide-gray-200'>
                    {filteredTransactions.map((transaction) => (
                      <tr key={transaction.id} className='hover:bg-gray-50'>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                          <div>
                            <div className='font-medium'>
                              {transaction.date}
                            </div>
                            <div className='text-gray-500'>
                              {transaction.time}
                            </div>
                          </div>
                        </td>
                        <td className='px-6 py-4 text-sm text-gray-900 pashto-text'>
                          {transaction.description}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm'>
                          {transaction.type === "جمعه" ? (
                            <span className='font-medium text-green-600'>
                              +{transaction.amount.toLocaleString()}{" "}
                              {transaction.currency}
                            </span>
                          ) : (
                            <span className='text-gray-400'>-</span>
                          )}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm'>
                          {transaction.type === "نام" ? (
                            <span className='font-medium text-red-600'>
                              -{transaction.amount.toLocaleString()}{" "}
                              {transaction.currency}
                            </span>
                          ) : (
                            <span className='text-gray-400'>-</span>
                          )}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm'>
                          <span
                            className={`font-medium ${
                              transaction.balance_after >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {transaction.balance_after >= 0 ? "+" : ""}
                            {transaction.balance_after.toLocaleString()} افغانۍ
                          </span>
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                          <div>
                            <div className='font-mono'>
                              {transaction.reference}
                            </div>
                            <div className='text-xs pashto-text'>
                              صفحه {transaction.daybook_page}
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {filteredTransactions.length === 0 && (
                  <div className='p-8 text-center text-gray-500 pashto-text'>
                    د دې پیرودونکي لپاره هیڅ راکړه ورکړه نه ده موندل شوې
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* که پیرودونکی نه وي انتخاب شوی */}
        {!selectedCustomer && (
          <div className='bg-white rounded-lg shadow-md p-12 text-center'>
            <User className='h-16 w-16 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2 pashto-text'>
              د پیرودونکي کاتا کتنه
            </h3>
            <p className='text-gray-500 pashto-text'>
              د کاتا د کتنې لپاره لومړی پیرودونکی انتخاب کړئ
            </p>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Ledger;
