import React, { useState, useEffect } from "react";
import {
  Plus,
  Save,
  Lock,
  Unlock,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Check,
  X,
} from "lucide-react";
import Layout from "./shared/Layout";
import Button from "./shared/Button";

const Daybook = () => {
  const [currentDaybook, setCurrentDaybook] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingRow, setEditingRow] = useState(null); // Track which row is being edited
  const [newRowData, setNewRowData] = useState({
    customer_name: "",
    amount: "",
    currency: "افغانۍ",
    description: "",
    type: "جمعه",
  });

  // د نمونه ډیټا
  const sampleDaybook = {
    id: 1,
    date: new Date().toLocaleDateString("fa-AF"),
    page_number: 15,
    status: "خلاص", // خلاص یا تړل شوی
    total_jamah: 125000,
    total_naam: 98000,
    balance: 27000,
  };

  const sampleTransactions = [
    {
      id: 1,
      index: 1,
      customer_name: "احمد علي خان",
      type: "جمعه",
      amount: 25000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۰:۳۰",
      balance_after: 25000,
    },
    {
      id: 2,
      index: 2,
      customer_name: "فاطمه احمد",
      type: "نام",
      amount: 15000,
      currency: "افغانۍ",
      description: "د پیسو ورکول",
      time: "۱۰:۴۵",
      balance_after: 10000,
    },
    {
      id: 3,
      index: 3,
      customer_name: "محمد حسن",
      type: "جمعه",
      amount: 50000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۱:۱۵",
      balance_after: 60000,
    },
  ];

  const customers = [
    { value: "1", label: "احمد علي خان" },
    { value: "2", label: "فاطمه احمد" },
    { value: "3", label: "محمد حسن" },
    { value: "4", label: "عایشه خان" },
    { value: "5", label: "علي رضا" },
  ];

  const currencies = [
    { value: "افغانۍ", label: "افغانۍ (AFN)" },
    { value: "ډالر", label: "ډالر (USD)" },
    { value: "یورو", label: "یورو (EUR)" },
    { value: "پاکستاني روپۍ", label: "پاکستاني روپۍ (PKR)" },
  ];

  useEffect(() => {
    loadDaybook();
  }, []);

  const loadDaybook = () => {
    setLoading(true);
    // د API د اړیکې لپاره وروسته به دا ځای بدل شي
    setTimeout(() => {
      setCurrentDaybook(sampleDaybook);
      setTransactions(sampleTransactions);
      setLoading(false);
    }, 1000);
  };

  // Handle inline editing functions
  const handleNewRowChange = (field, value) => {
    setNewRowData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addNewTransaction = (type) => {
    if (
      !newRowData.customer_name.trim() ||
      !newRowData.amount ||
      !newRowData.description.trim()
    ) {
      alert("لطفاً ټول ضروري ساحې ډک کړئ");
      return;
    }

    const amount = parseFloat(newRowData.amount);
    if (amount <= 0) {
      alert("د پیسو سمه اندازه ولیکئ");
      return;
    }

    const lastBalance =
      transactions.length > 0
        ? transactions[transactions.length - 1].balance_after
        : 0;
    const newBalance =
      type === "جمعه" ? lastBalance + amount : lastBalance - amount;

    const newTransaction = {
      id: transactions.length + 1,
      index: transactions.length + 1,
      customer_name: newRowData.customer_name,
      type: type,
      amount: amount,
      currency: newRowData.currency,
      description: newRowData.description,
      time: new Date().toLocaleTimeString("fa-AF", {
        hour: "2-digit",
        minute: "2-digit",
      }),
      balance_after: newBalance,
    };

    setTransactions((prev) => [...prev, newTransaction]);

    // Update daybook totals
    const newTotalJamah =
      transactions
        .filter((t) => t.type === "جمعه")
        .reduce((sum, t) => sum + t.amount, 0) + (type === "جمعه" ? amount : 0);

    const newTotalNaam =
      transactions
        .filter((t) => t.type === "نام")
        .reduce((sum, t) => sum + t.amount, 0) + (type === "نام" ? amount : 0);

    setCurrentDaybook((prev) => ({
      ...prev,
      total_jamah: newTotalJamah,
      total_naam: newTotalNaam,
      balance: newTotalJamah - newTotalNaam,
    }));

    // Reset form
    setNewRowData({
      customer_name: "",
      amount: "",
      currency: "افغانۍ",
      description: "",
      type: "جمعه",
    });
    setEditingRow(null);
  };

  const startNewRow = (type) => {
    setEditingRow(type);
    setNewRowData((prev) => ({ ...prev, type }));
  };

  const cancelNewRow = () => {
    setEditingRow(null);
    setNewRowData({
      customer_name: "",
      amount: "",
      currency: "افغانۍ",
      description: "",
      type: "جمعه",
    });
  };

  const toggleDaybookStatus = () => {
    setCurrentDaybook((prev) => ({
      ...prev,
      status: prev.status === "خلاص" ? "تړل شوی" : "خلاص",
    }));
  };

  if (loading) {
    return (
      <Layout title='ورځني کتاب (روزنامچه)'>
        <div className='flex items-center justify-center h-64'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-sarafi-600'></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title='ورځني کتاب (روزنامچه)'>
      <div className='space-y-6'>
        {/* د ورځني کتاب سرلیک */}
        <div className='bg-white rounded-lg shadow-md p-6'>
          <div className='flex items-center justify-between mb-4'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
                ورځني کتاب - صفحه {currentDaybook?.page_number}
              </h1>
              <div className='flex items-center space-x-4 mt-2'>
                <span className='text-gray-600 pashto-text'>
                  نیټه: {currentDaybook?.date}
                </span>
                <span
                  className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    currentDaybook?.status === "خلاص"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {currentDaybook?.status}
                </span>
              </div>
            </div>

            <div className='flex items-center space-x-3'>
              <Button variant='outline' icon={Calendar} onClick={() => {}}>
                نوی ورځني کتاب
              </Button>

              <Button
                variant={
                  currentDaybook?.status === "خلاص" ? "danger" : "success"
                }
                icon={currentDaybook?.status === "خلاص" ? Lock : Unlock}
                onClick={toggleDaybookStatus}
              >
                {currentDaybook?.status === "خلاص" ? "تړل" : "خلاصول"}
              </Button>
            </div>
          </div>

          {/* د ټولو لنډیز */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-green-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowUpRight className='h-8 w-8 text-green-600 ml-3' />
                <div>
                  <p className='text-sm text-green-600 pashto-text'>
                    ټوله جمعه
                  </p>
                  <p className='text-xl font-bold text-green-700'>
                    {currentDaybook?.total_jamah?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-red-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowDownRight className='h-8 w-8 text-red-600 ml-3' />
                <div>
                  <p className='text-sm text-red-600 pashto-text'>ټول نام</p>
                  <p className='text-xl font-bold text-red-700'>
                    {currentDaybook?.total_naam?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-blue-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Save className='h-8 w-8 text-blue-600 ml-3' />
                <div>
                  <p className='text-sm text-blue-600 pashto-text'>میزان</p>
                  <p
                    className={`text-xl font-bold ${
                      currentDaybook?.balance >= 0
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    {currentDaybook?.balance >= 0 ? "+" : ""}
                    {currentDaybook?.balance?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-gray-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Calendar className='h-8 w-8 text-gray-600 ml-3' />
                <div>
                  <p className='text-sm text-gray-600 pashto-text'>
                    ټولې راکړې ورکړې
                  </p>
                  <p className='text-xl font-bold text-gray-700'>
                    {transactions.length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* د راکړو ورکړو برخه - Excel Style Table */}
        <div className='bg-white rounded-lg shadow-md'>
          <div className='px-6 py-4 border-b border-gray-200'>
            <h3 className='text-lg font-semibold text-gray-900 pashto-text'>
              د ورځني کتاب راکړې ورکړې
            </h3>
          </div>

          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200'>
              <thead className='bg-gray-50'>
                <tr>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                    #
                  </th>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                    پیرودونکی
                  </th>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                    تفصیل
                  </th>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                    جمعه
                  </th>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                    نام
                  </th>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                    پیسې
                  </th>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                    میزان
                  </th>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider pashto-text'>
                    وخت
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {transactions.map((transaction, index) => (
                  <tr key={transaction.id} className='hover:bg-gray-50'>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                      {index + 1}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 pashto-text'>
                      {transaction.customer_name}
                    </td>
                    <td className='px-6 py-4 text-sm text-gray-900 pashto-text'>
                      {transaction.description}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm'>
                      {transaction.type === "جمعه" ? (
                        <span className='text-green-600 font-medium'>
                          +{transaction.amount.toLocaleString()}
                        </span>
                      ) : (
                        <span className='text-gray-400'>-</span>
                      )}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm'>
                      {transaction.type === "نام" ? (
                        <span className='text-red-600 font-medium'>
                          -{transaction.amount.toLocaleString()}
                        </span>
                      ) : (
                        <span className='text-gray-400'>-</span>
                      )}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 pashto-text'>
                      {transaction.currency}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                      {transaction.balance_after.toLocaleString()}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {transaction.time}
                    </td>
                  </tr>
                ))}

                {/* Add new row for جمعه */}
                {editingRow === "جمعه" && (
                  <tr className='bg-green-50 border-2 border-green-200'>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                      {transactions.length + 1}
                    </td>
                    <td className='px-6 py-4'>
                      <input
                        type='text'
                        value={newRowData.customer_name}
                        onChange={(e) =>
                          handleNewRowChange("customer_name", e.target.value)
                        }
                        placeholder='د پیرودونکي نوم'
                        className='w-full px-2 py-1 border border-gray-300 rounded text-sm pashto-text'
                        dir='rtl'
                      />
                    </td>
                    <td className='px-6 py-4'>
                      <input
                        type='text'
                        value={newRowData.description}
                        onChange={(e) =>
                          handleNewRowChange("description", e.target.value)
                        }
                        placeholder='تفصیل'
                        className='w-full px-2 py-1 border border-gray-300 rounded text-sm pashto-text'
                        dir='rtl'
                      />
                    </td>
                    <td className='px-6 py-4'>
                      <input
                        type='number'
                        value={newRowData.amount}
                        onChange={(e) =>
                          handleNewRowChange("amount", e.target.value)
                        }
                        placeholder='اندازه'
                        className='w-full px-2 py-1 border border-gray-300 rounded text-sm'
                        dir='ltr'
                      />
                    </td>
                    <td className='px-6 py-4'>
                      <span className='text-gray-400'>-</span>
                    </td>
                    <td className='px-6 py-4'>
                      <select
                        value={newRowData.currency}
                        onChange={(e) =>
                          handleNewRowChange("currency", e.target.value)
                        }
                        className='w-full px-2 py-1 border border-gray-300 rounded text-sm pashto-text'
                      >
                        <option value='افغانۍ'>افغانۍ</option>
                        <option value='ډالر'>ډالر</option>
                        <option value='یورو'>یورو</option>
                        <option value='پاکستاني روپۍ'>پاکستاني روپۍ</option>
                      </select>
                    </td>
                    <td className='px-6 py-4'>
                      <span className='text-gray-400'>-</span>
                    </td>
                    <td className='px-6 py-4'>
                      <div className='flex space-x-2'>
                        <button
                          onClick={() => addNewTransaction("جمعه")}
                          className='p-1 text-green-600 hover:bg-green-100 rounded'
                        >
                          <Check className='h-4 w-4' />
                        </button>
                        <button
                          onClick={cancelNewRow}
                          className='p-1 text-red-600 hover:bg-red-100 rounded'
                        >
                          <X className='h-4 w-4' />
                        </button>
                      </div>
                    </td>
                  </tr>
                )}

                {/* Add new row for نام */}
                {editingRow === "نام" && (
                  <tr className='bg-red-50 border-2 border-red-200'>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                      {transactions.length + 1}
                    </td>
                    <td className='px-6 py-4'>
                      <input
                        type='text'
                        value={newRowData.customer_name}
                        onChange={(e) =>
                          handleNewRowChange("customer_name", e.target.value)
                        }
                        placeholder='د پیرودونکي نوم'
                        className='w-full px-2 py-1 border border-gray-300 rounded text-sm pashto-text'
                        dir='rtl'
                      />
                    </td>
                    <td className='px-6 py-4'>
                      <input
                        type='text'
                        value={newRowData.description}
                        onChange={(e) =>
                          handleNewRowChange("description", e.target.value)
                        }
                        placeholder='تفصیل'
                        className='w-full px-2 py-1 border border-gray-300 rounded text-sm pashto-text'
                        dir='rtl'
                      />
                    </td>
                    <td className='px-6 py-4'>
                      <span className='text-gray-400'>-</span>
                    </td>
                    <td className='px-6 py-4'>
                      <input
                        type='number'
                        value={newRowData.amount}
                        onChange={(e) =>
                          handleNewRowChange("amount", e.target.value)
                        }
                        placeholder='اندازه'
                        className='w-full px-2 py-1 border border-gray-300 rounded text-sm'
                        dir='ltr'
                      />
                    </td>
                    <td className='px-6 py-4'>
                      <select
                        value={newRowData.currency}
                        onChange={(e) =>
                          handleNewRowChange("currency", e.target.value)
                        }
                        className='w-full px-2 py-1 border border-gray-300 rounded text-sm pashto-text'
                      >
                        <option value='افغانۍ'>افغانۍ</option>
                        <option value='ډالر'>ډالر</option>
                        <option value='یورو'>یورو</option>
                        <option value='پاکستاني روپۍ'>پاکستاني روپۍ</option>
                      </select>
                    </td>
                    <td className='px-6 py-4'>
                      <span className='text-gray-400'>-</span>
                    </td>
                    <td className='px-6 py-4'>
                      <div className='flex space-x-2'>
                        <button
                          onClick={() => addNewTransaction("نام")}
                          className='p-1 text-green-600 hover:bg-green-100 rounded'
                        >
                          <Check className='h-4 w-4' />
                        </button>
                        <button
                          onClick={cancelNewRow}
                          className='p-1 text-red-600 hover:bg-red-100 rounded'
                        >
                          <X className='h-4 w-4' />
                        </button>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Action buttons */}
          <div className='px-6 py-4 bg-gray-50 border-t border-gray-200'>
            <div className='flex justify-center space-x-4'>
              <Button
                variant='success'
                icon={Plus}
                onClick={() => startNewRow("جمعه")}
                disabled={
                  currentDaybook?.status === "تړل شوی" || editingRow !== null
                }
              >
                نوې جمعه اضافه کړئ
              </Button>
              <Button
                variant='danger'
                icon={Plus}
                onClick={() => startNewRow("نام")}
                disabled={
                  currentDaybook?.status === "تړل شوی" || editingRow !== null
                }
              >
                نوی نام اضافه کړئ
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Daybook;
