import React, { useState, useEffect } from "react";
import {
  Plus,
  Save,
  Lock,
  Unlock,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Check,
  X,
} from "lucide-react";
import Layout from "./shared/Layout";
import Button from "./shared/Button";

const Daybook = () => {
  const [currentDaybook, setCurrentDaybook] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("جمعه"); // Track active sheet
  const [jamaRows, setJamaRows] = useState([]);
  const [namRows, setNamRows] = useState([]);

  // د نمونه ډیټا
  const sampleDaybook = {
    id: 1,
    date: new Date().toLocaleDateString("fa-AF"),
    page_number: 15,
    status: "خلاص", // خلاص یا تړل شوی
    total_jamah: 125000,
    total_naam: 98000,
    balance: 27000,
  };

  const sampleTransactions = [
    {
      id: 1,
      index: 1,
      customer_name: "احمد علي خان",
      type: "جمعه",
      amount: 25000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۰:۳۰",
      balance_after: 25000,
    },
    {
      id: 2,
      index: 2,
      customer_name: "فاطمه احمد",
      type: "نام",
      amount: 15000,
      currency: "افغانۍ",
      description: "د پیسو ورکول",
      time: "۱۰:۴۵",
      balance_after: 10000,
    },
    {
      id: 3,
      index: 3,
      customer_name: "محمد حسن",
      type: "جمعه",
      amount: 50000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۱:۱۵",
      balance_after: 60000,
    },
  ];

  const customers = [
    { value: "1", label: "احمد علي خان" },
    { value: "2", label: "فاطمه احمد" },
    { value: "3", label: "محمد حسن" },
    { value: "4", label: "عایشه خان" },
    { value: "5", label: "علي رضا" },
  ];

  const currencies = [
    { value: "افغانۍ", label: "افغانۍ (AFN)" },
    { value: "ډالر", label: "ډالر (USD)" },
    { value: "یورو", label: "یورو (EUR)" },
    { value: "پاکستاني روپۍ", label: "پاکستاني روپۍ (PKR)" },
  ];

  useEffect(() => {
    loadDaybook();
  }, []);

  const loadDaybook = () => {
    setLoading(true);
    // د API د اړیکې لپاره وروسته به دا ځای بدل شي
    setTimeout(() => {
      setCurrentDaybook(sampleDaybook);
      setTransactions(sampleTransactions);

      // Initialize empty rows for both sheets
      const initialJamaRows = Array(10)
        .fill(null)
        .map((_, index) => ({
          id: index + 1,
          customer_name: "",
          description: "",
          amount: "",
          currency: "افغانۍ",
          time: "",
          balance: "",
        }));

      const initialNamRows = Array(10)
        .fill(null)
        .map((_, index) => ({
          id: index + 1,
          customer_name: "",
          description: "",
          amount: "",
          currency: "افغانۍ",
          time: "",
          balance: "",
        }));

      setJamaRows(initialJamaRows);
      setNamRows(initialNamRows);
      setLoading(false);
    }, 1000);
  };

  // Handle cell input changes
  const handleCellChange = (rowIndex, field, value, sheetType) => {
    if (sheetType === "جمعه") {
      setJamaRows((prev) => {
        const newRows = [...prev];
        newRows[rowIndex] = { ...newRows[rowIndex], [field]: value };

        // Auto-fill time when amount is entered
        if (field === "amount" && value) {
          newRows[rowIndex].time = new Date().toLocaleTimeString("fa-AF", {
            hour: "2-digit",
            minute: "2-digit",
          });
        }

        return newRows;
      });
    } else {
      setNamRows((prev) => {
        const newRows = [...prev];
        newRows[rowIndex] = { ...newRows[rowIndex], [field]: value };

        // Auto-fill time when amount is entered
        if (field === "amount" && value) {
          newRows[rowIndex].time = new Date().toLocaleTimeString("fa-AF", {
            hour: "2-digit",
            minute: "2-digit",
          });
        }

        return newRows;
      });
    }
  };

  // Handle Enter key press to move to next row
  const handleKeyPress = (e, rowIndex, field, sheetType) => {
    if (e.key === "Enter") {
      e.preventDefault();

      // Check if current row has data
      const currentRows = sheetType === "جمعه" ? jamaRows : namRows;
      const currentRow = currentRows[rowIndex];

      // If current row has some data, move to next row
      if (
        currentRow.customer_name ||
        currentRow.description ||
        currentRow.amount
      ) {
        // If this is the last row, add a new one
        if (rowIndex === currentRows.length - 1) {
          addMoreRows(sheetType);
        }

        // Focus next row's first input
        setTimeout(() => {
          const nextRowInput = document.querySelector(
            `[data-row="${rowIndex + 1}"][data-field="customer_name"]`
          );
          if (nextRowInput) {
            nextRowInput.focus();
          }
        }, 100);
      }
    }
  };

  // Add more rows when needed
  const addMoreRows = (sheetType) => {
    const newRows = Array(5)
      .fill(null)
      .map((_, index) => ({
        id:
          (sheetType === "جمعه" ? jamaRows.length : namRows.length) + index + 1,
        customer_name: "",
        description: "",
        amount: "",
        currency: "افغانۍ",
        time: "",
        balance: "",
      }));

    if (sheetType === "جمعه") {
      setJamaRows((prev) => [...prev, ...newRows]);
    } else {
      setNamRows((prev) => [...prev, ...newRows]);
    }
  };

  const toggleDaybookStatus = () => {
    setCurrentDaybook((prev) => ({
      ...prev,
      status: prev.status === "خلاص" ? "تړل شوی" : "خلاص",
    }));
  };

  if (loading) {
    return (
      <Layout title='ورځني کتاب (روزنامچه)'>
        <div className='flex items-center justify-center h-64'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-sarafi-600'></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title='ورځني کتاب (روزنامچه)'>
      <div className='space-y-6'>
        {/* د ورځني کتاب سرلیک */}
        <div className='bg-white rounded-lg shadow-md p-6'>
          <div className='flex items-center justify-between mb-4'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
                ورځني کتاب - صفحه {currentDaybook?.page_number}
              </h1>
              <div className='flex items-center space-x-4 mt-2'>
                <span className='text-gray-600 pashto-text'>
                  نیټه: {currentDaybook?.date}
                </span>
                <span
                  className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    currentDaybook?.status === "خلاص"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {currentDaybook?.status}
                </span>
              </div>
            </div>

            <div className='flex items-center space-x-3'>
              <Button variant='outline' icon={Calendar} onClick={() => {}}>
                نوی ورځني کتاب
              </Button>

              <Button
                variant={
                  currentDaybook?.status === "خلاص" ? "danger" : "success"
                }
                icon={currentDaybook?.status === "خلاص" ? Lock : Unlock}
                onClick={toggleDaybookStatus}
              >
                {currentDaybook?.status === "خلاص" ? "تړل" : "خلاصول"}
              </Button>
            </div>
          </div>

          {/* د ټولو لنډیز */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-green-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowUpRight className='h-8 w-8 text-green-600 ml-3' />
                <div>
                  <p className='text-sm text-green-600 pashto-text'>
                    ټوله جمعه
                  </p>
                  <p className='text-xl font-bold text-green-700'>
                    {currentDaybook?.total_jamah?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-red-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowDownRight className='h-8 w-8 text-red-600 ml-3' />
                <div>
                  <p className='text-sm text-red-600 pashto-text'>ټول نام</p>
                  <p className='text-xl font-bold text-red-700'>
                    {currentDaybook?.total_naam?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-blue-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Save className='h-8 w-8 text-blue-600 ml-3' />
                <div>
                  <p className='text-sm text-blue-600 pashto-text'>میزان</p>
                  <p
                    className={`text-xl font-bold ${
                      currentDaybook?.balance >= 0
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    {currentDaybook?.balance >= 0 ? "+" : ""}
                    {currentDaybook?.balance?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-gray-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Calendar className='h-8 w-8 text-gray-600 ml-3' />
                <div>
                  <p className='text-sm text-gray-600 pashto-text'>
                    ټولې راکړې ورکړې
                  </p>
                  <p className='text-xl font-bold text-gray-700'>
                    {transactions.length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Excel-like Sheets with Tabs */}
        <div className='bg-white rounded-lg shadow-md'>
          {/* Sheet Tabs */}
          <div className='border-b border-gray-200'>
            <nav className='-mb-px flex'>
              <button
                onClick={() => setActiveTab("جمعه")}
                className={`py-2 px-6 border-b-2 font-medium text-sm pashto-text ${
                  activeTab === "جمعه"
                    ? "border-green-500 text-green-600 bg-green-50"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                جمعه (اخیستل)
              </button>
              <button
                onClick={() => setActiveTab("نام")}
                className={`py-2 px-6 border-b-2 font-medium text-sm pashto-text ${
                  activeTab === "نام"
                    ? "border-red-500 text-red-600 bg-red-50"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                نام (ورکول)
              </button>
            </nav>
          </div>

          {/* Excel-like Table */}
          <div className='overflow-x-auto'>
            <table className='min-w-full border-collapse'>
              <thead
                className={`${
                  activeTab === "جمعه" ? "bg-green-50" : "bg-red-50"
                }`}
              >
                <tr>
                  <th className='border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700 w-12'>
                    #
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-gray-700 pashto-text min-w-[150px]'>
                    پیرودونکی
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-gray-700 pashto-text min-w-[200px]'>
                    تفصیل
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-gray-700 pashto-text min-w-[120px]'>
                    اندازه
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-gray-700 pashto-text min-w-[100px]'>
                    پیسې
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-gray-700 pashto-text min-w-[100px]'>
                    وخت
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white'>
                {(activeTab === "جمعه" ? jamaRows : namRows).map(
                  (row, index) => (
                    <tr key={row.id} className='hover:bg-gray-50'>
                      <td className='border border-gray-300 px-3 py-1 text-center text-sm text-gray-700'>
                        {index + 1}
                      </td>
                      <td className='border border-gray-300 px-1 py-1'>
                        <input
                          type='text'
                          value={row.customer_name}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "customer_name",
                              e.target.value,
                              activeTab
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "customer_name", activeTab)
                          }
                          data-row={index}
                          data-field='customer_name'
                          className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none pashto-text'
                          placeholder='نوم ولیکئ'
                          dir='rtl'
                        />
                      </td>
                      <td className='border border-gray-300 px-1 py-1'>
                        <input
                          type='text'
                          value={row.description}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "description",
                              e.target.value,
                              activeTab
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "description", activeTab)
                          }
                          data-row={index}
                          data-field='description'
                          className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none pashto-text'
                          placeholder='تفصیل ولیکئ'
                          dir='rtl'
                        />
                      </td>
                      <td className='border border-gray-300 px-1 py-1'>
                        <input
                          type='number'
                          value={row.amount}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "amount",
                              e.target.value,
                              activeTab
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "amount", activeTab)
                          }
                          data-row={index}
                          data-field='amount'
                          className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none'
                          placeholder='0'
                          dir='ltr'
                        />
                      </td>
                      <td className='border border-gray-300 px-1 py-1'>
                        <select
                          value={row.currency}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "currency",
                              e.target.value,
                              activeTab
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "currency", activeTab)
                          }
                          data-row={index}
                          data-field='currency'
                          className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none pashto-text'
                        >
                          <option value='افغانۍ'>افغانۍ</option>
                          <option value='ډالر'>ډالر</option>
                          <option value='یورو'>یورو</option>
                          <option value='پاکستاني روپۍ'>پاکستاني روپۍ</option>
                        </select>
                      </td>
                      <td className='border border-gray-300 px-3 py-1 text-center text-sm text-gray-500'>
                        {row.time}
                      </td>
                    </tr>
                  )
                )}
              </tbody>
            </table>
          </div>

          {/* Add more rows button */}
          <div className='px-6 py-3 bg-gray-50 border-t border-gray-200 text-center'>
            <Button
              variant='outline'
              icon={Plus}
              onClick={() => addMoreRows(activeTab)}
              disabled={currentDaybook?.status === "تړل شوی"}
            >
              نور قطارونه اضافه کړئ
            </Button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Daybook;
