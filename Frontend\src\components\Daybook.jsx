import React, { useState, useEffect } from "react";
import {
  Plus,
  Save,
  Lock,
  Unlock,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Check,
  X,
} from "lucide-react";
import Layout from "./shared/Layout";
import Button from "./shared/Button";

const Daybook = () => {
  const [currentDaybook, setCurrentDaybook] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("جمعه"); // Track active sheet
  const [jamaRows, setJamaRows] = useState([]);
  const [namRows, setNamRows] = useState([]);

  // د نمونه ډیټا
  const sampleDaybook = {
    id: 1,
    date: new Date().toLocaleDateString("fa-AF"),
    page_number: 15,
    status: "خلاص", // خلاص یا تړل شوی
    total_jamah: 125000,
    total_naam: 98000,
    balance: 27000,
  };

  const sampleTransactions = [
    {
      id: 1,
      index: 1,
      customer_name: "احمد علي خان",
      type: "جمعه",
      amount: 25000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۰:۳۰",
      balance_after: 25000,
    },
    {
      id: 2,
      index: 2,
      customer_name: "فاطمه احمد",
      type: "نام",
      amount: 15000,
      currency: "افغانۍ",
      description: "د پیسو ورکول",
      time: "۱۰:۴۵",
      balance_after: 10000,
    },
    {
      id: 3,
      index: 3,
      customer_name: "محمد حسن",
      type: "جمعه",
      amount: 50000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۱:۱۵",
      balance_after: 60000,
    },
  ];

  const customers = [
    { value: "1", label: "احمد علي خان" },
    { value: "2", label: "فاطمه احمد" },
    { value: "3", label: "محمد حسن" },
    { value: "4", label: "عایشه خان" },
    { value: "5", label: "علي رضا" },
  ];

  const currencies = [
    { value: "افغانۍ", label: "افغانۍ (AFN)" },
    { value: "ډالر", label: "ډالر (USD)" },
    { value: "یورو", label: "یورو (EUR)" },
    { value: "پاکستاني روپۍ", label: "پاکستاني روپۍ (PKR)" },
  ];

  useEffect(() => {
    loadDaybook();
  }, []);

  const loadDaybook = () => {
    setLoading(true);
    // د API د اړیکې لپاره وروسته به دا ځای بدل شي
    setTimeout(() => {
      setCurrentDaybook(sampleDaybook);
      setTransactions(sampleTransactions);

      // Initialize with one empty row
      const initialRow = {
        id: 1,
        customer_name: "",
        description: "",
        amount: "",
        currency: "افغانۍ",
        time: "",
        balance: "",
      };

      setJamaRows([initialRow]);
      setNamRows([{ ...initialRow }]);
      setLoading(false);
    }, 1000);
  };

  // Handle cell input changes
  const handleCellChange = (rowIndex, field, value, sheetType) => {
    if (sheetType === "جمعه") {
      setJamaRows((prev) => {
        const newRows = [...prev];
        newRows[rowIndex] = { ...newRows[rowIndex], [field]: value };

        // Auto-fill time when amount is entered
        if (field === "amount" && value) {
          newRows[rowIndex].time = new Date().toLocaleTimeString("fa-AF", {
            hour: "2-digit",
            minute: "2-digit",
          });
        }

        return newRows;
      });
    } else {
      setNamRows((prev) => {
        const newRows = [...prev];
        newRows[rowIndex] = { ...newRows[rowIndex], [field]: value };

        // Auto-fill time when amount is entered
        if (field === "amount" && value) {
          newRows[rowIndex].time = new Date().toLocaleTimeString("fa-AF", {
            hour: "2-digit",
            minute: "2-digit",
          });
        }

        return newRows;
      });
    }
  };

  // Handle Enter key press to move to next row
  const handleKeyPress = (e, rowIndex, field, sheetType) => {
    if (e.key === "Enter") {
      e.preventDefault();

      // Check if current row has data in any field
      const jamaRow = jamaRows[rowIndex];
      const namRow = namRows[rowIndex];

      const hasData =
        (jamaRow &&
          (jamaRow.customer_name || jamaRow.description || jamaRow.amount)) ||
        (namRow &&
          (namRow.customer_name || namRow.description || namRow.amount));

      if (hasData) {
        // If this is the last row, add a new one
        const maxLength = Math.max(jamaRows.length, namRows.length);
        if (rowIndex === maxLength - 1) {
          // Add new row to both arrays to keep them in sync
          const newJamaRow = {
            id: jamaRows.length + 1,
            customer_name: "",
            description: "",
            amount: "",
            currency: "افغانۍ",
            time: "",
            balance: "",
          };
          const newNamRow = {
            id: namRows.length + 1,
            customer_name: "",
            description: "",
            amount: "",
            currency: "افغانۍ",
            time: "",
            balance: "",
          };

          setJamaRows((prev) => [...prev, newJamaRow]);
          setNamRows((prev) => [...prev, newNamRow]);
        }

        // Focus next row's first input
        setTimeout(() => {
          const nextRowInput = document.querySelector(
            `[data-row="${rowIndex + 1}"][data-field="customer_name"]`
          );
          if (nextRowInput) {
            nextRowInput.focus();
          }
        }, 100);
      }
    }
  };

  // Add single row when needed
  const addSingleRow = (sheetType) => {
    const newRow = {
      id: (sheetType === "جمعه" ? jamaRows.length : namRows.length) + 1,
      customer_name: "",
      description: "",
      amount: "",
      currency: "افغانۍ",
      time: "",
      balance: "",
    };

    if (sheetType === "جمعه") {
      setJamaRows((prev) => [...prev, newRow]);
    } else {
      setNamRows((prev) => [...prev, newRow]);
    }
  };

  // Delete row function
  const deleteRow = (rowIndex, sheetType) => {
    if (sheetType === "جمعه") {
      setJamaRows((prev) => prev.filter((_, index) => index !== rowIndex));
    } else {
      setNamRows((prev) => prev.filter((_, index) => index !== rowIndex));
    }
  };

  const toggleDaybookStatus = () => {
    setCurrentDaybook((prev) => ({
      ...prev,
      status: prev.status === "خلاص" ? "تړل شوی" : "خلاص",
    }));
  };

  if (loading) {
    return (
      <Layout title='ورځني کتاب (روزنامچه)'>
        <div className='flex items-center justify-center h-64'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-sarafi-600'></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title='ورځني کتاب (روزنامچه)'>
      <div className='space-y-6'>
        {/* د ورځني کتاب سرلیک */}
        <div className='bg-white rounded-lg shadow-md p-6'>
          <div className='flex items-center justify-between mb-4'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
                ورځني کتاب - صفحه {currentDaybook?.page_number}
              </h1>
              <div className='flex items-center space-x-4 mt-2'>
                <span className='text-gray-600 pashto-text'>
                  نیټه: {currentDaybook?.date}
                </span>
                <span
                  className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    currentDaybook?.status === "خلاص"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {currentDaybook?.status}
                </span>
              </div>
            </div>

            <div className='flex items-center space-x-3'>
              <Button variant='outline' icon={Calendar} onClick={() => {}}>
                نوی ورځني کتاب
              </Button>

              <Button
                variant={
                  currentDaybook?.status === "خلاص" ? "danger" : "success"
                }
                icon={currentDaybook?.status === "خلاص" ? Lock : Unlock}
                onClick={toggleDaybookStatus}
              >
                {currentDaybook?.status === "خلاص" ? "تړل" : "خلاصول"}
              </Button>
            </div>
          </div>

          {/* د ټولو لنډیز */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-green-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowUpRight className='h-8 w-8 text-green-600 ml-3' />
                <div>
                  <p className='text-sm text-green-600 pashto-text'>
                    ټوله جمعه
                  </p>
                  <p className='text-xl font-bold text-green-700'>
                    {currentDaybook?.total_jamah?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-red-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowDownRight className='h-8 w-8 text-red-600 ml-3' />
                <div>
                  <p className='text-sm text-red-600 pashto-text'>ټول نام</p>
                  <p className='text-xl font-bold text-red-700'>
                    {currentDaybook?.total_naam?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-blue-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Save className='h-8 w-8 text-blue-600 ml-3' />
                <div>
                  <p className='text-sm text-blue-600 pashto-text'>میزان</p>
                  <p
                    className={`text-xl font-bold ${
                      currentDaybook?.balance >= 0
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    {currentDaybook?.balance >= 0 ? "+" : ""}
                    {currentDaybook?.balance?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-gray-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Calendar className='h-8 w-8 text-gray-600 ml-3' />
                <div>
                  <p className='text-sm text-gray-600 pashto-text'>
                    ټولې راکړې ورکړې
                  </p>
                  <p className='text-xl font-bold text-gray-700'>
                    {transactions.length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Excel-like Table with Nam and Jama side by side */}
        <div className='bg-white rounded-lg shadow-md'>
          <div className='px-6 py-4 border-b border-gray-200'>
            <h3 className='text-lg font-semibold text-gray-900 pashto-text'>
              د ورځني کتاب راکړې ورکړې
            </h3>
          </div>

          <div className='overflow-x-auto'>
            <table className='min-w-full border-collapse'>
              <thead className='bg-gray-50'>
                <tr>
                  <th className='border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700 w-12'>
                    #
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-gray-700 pashto-text min-w-[150px]'>
                    پیرودونکی
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-gray-700 pashto-text min-w-[200px]'>
                    تفصیل
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-green-700 pashto-text min-w-[120px] bg-green-50'>
                    جمعه
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-red-700 pashto-text min-w-[120px] bg-red-50'>
                    نام
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-gray-700 pashto-text min-w-[100px]'>
                    پیسې
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-right text-xs font-medium text-gray-700 pashto-text min-w-[100px]'>
                    وخت
                  </th>
                  <th className='border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700 w-16'>
                    عمل
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white'>
                {Math.max(jamaRows.length, namRows.length) > 0 &&
                  Array.from(
                    { length: Math.max(jamaRows.length, namRows.length) },
                    (_, index) => {
                      const jamaRow = jamaRows[index] || {
                        customer_name: "",
                        description: "",
                        amount: "",
                        currency: "افغانۍ",
                        time: "",
                      };
                      const namRow = namRows[index] || {
                        customer_name: "",
                        description: "",
                        amount: "",
                        currency: "افغانۍ",
                        time: "",
                      };

                      return (
                        <tr key={index} className='hover:bg-gray-50'>
                          <td className='border border-gray-300 px-3 py-1 text-center text-sm text-gray-700'>
                            {index + 1}
                          </td>
                          <td className='border border-gray-300 px-1 py-1'>
                            <input
                              type='text'
                              value={
                                jamaRow.customer_name || namRow.customer_name
                              }
                              onChange={(e) => {
                                if (jamaRows[index])
                                  handleCellChange(
                                    index,
                                    "customer_name",
                                    e.target.value,
                                    "جمعه"
                                  );
                                if (namRows[index])
                                  handleCellChange(
                                    index,
                                    "customer_name",
                                    e.target.value,
                                    "نام"
                                  );
                              }}
                              onKeyPress={(e) =>
                                handleKeyPress(
                                  e,
                                  index,
                                  "customer_name",
                                  "جمعه"
                                )
                              }
                              data-row={index}
                              data-field='customer_name'
                              className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none pashto-text'
                              placeholder='نوم ولیکئ'
                              dir='rtl'
                            />
                          </td>
                          <td className='border border-gray-300 px-1 py-1'>
                            <input
                              type='text'
                              value={jamaRow.description || namRow.description}
                              onChange={(e) => {
                                if (jamaRows[index])
                                  handleCellChange(
                                    index,
                                    "description",
                                    e.target.value,
                                    "جمعه"
                                  );
                                if (namRows[index])
                                  handleCellChange(
                                    index,
                                    "description",
                                    e.target.value,
                                    "نام"
                                  );
                              }}
                              onKeyPress={(e) =>
                                handleKeyPress(e, index, "description", "جمعه")
                              }
                              data-row={index}
                              data-field='description'
                              className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none pashto-text'
                              placeholder='تفصیل ولیکئ'
                              dir='rtl'
                            />
                          </td>
                          <td className='border border-gray-300 px-1 py-1 bg-green-50'>
                            <input
                              type='number'
                              value={jamaRow.amount}
                              onChange={(e) =>
                                handleCellChange(
                                  index,
                                  "amount",
                                  e.target.value,
                                  "جمعه"
                                )
                              }
                              onKeyPress={(e) =>
                                handleKeyPress(e, index, "jama_amount", "جمعه")
                              }
                              data-row={index}
                              data-field='jama_amount'
                              className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none'
                              placeholder='0'
                              dir='ltr'
                            />
                          </td>
                          <td className='border border-gray-300 px-1 py-1 bg-red-50'>
                            <input
                              type='number'
                              value={namRow.amount}
                              onChange={(e) =>
                                handleCellChange(
                                  index,
                                  "amount",
                                  e.target.value,
                                  "نام"
                                )
                              }
                              onKeyPress={(e) =>
                                handleKeyPress(e, index, "nam_amount", "نام")
                              }
                              data-row={index}
                              data-field='nam_amount'
                              className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none'
                              placeholder='0'
                              dir='ltr'
                            />
                          </td>
                          <td className='border border-gray-300 px-1 py-1'>
                            <select
                              value={jamaRow.currency || namRow.currency}
                              onChange={(e) => {
                                if (jamaRows[index])
                                  handleCellChange(
                                    index,
                                    "currency",
                                    e.target.value,
                                    "جمعه"
                                  );
                                if (namRows[index])
                                  handleCellChange(
                                    index,
                                    "currency",
                                    e.target.value,
                                    "نام"
                                  );
                              }}
                              onKeyPress={(e) =>
                                handleKeyPress(e, index, "currency", "جمعه")
                              }
                              data-row={index}
                              data-field='currency'
                              className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none pashto-text'
                            >
                              <option value='افغانۍ'>افغانۍ</option>
                              <option value='ډالر'>ډالر</option>
                              <option value='یورو'>یورو</option>
                              <option value='پاکستاني روپۍ'>
                                پاکستاني روپۍ
                              </option>
                            </select>
                          </td>
                          <td className='border border-gray-300 px-3 py-1 text-center text-sm text-gray-500'>
                            {jamaRow.time || namRow.time}
                          </td>
                          <td className='border border-gray-300 px-1 py-1 text-center'>
                            <button
                              onClick={() => {
                                if (jamaRows[index]) deleteRow(index, "جمعه");
                                if (namRows[index]) deleteRow(index, "نام");
                              }}
                              className='p-1 text-red-600 hover:bg-red-100 rounded'
                              title='قطار ړنګ کړئ'
                            >
                              <X className='h-4 w-4' />
                            </button>
                          </td>
                        </tr>
                      );
                    }
                  )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Daybook;
