import React, { useState, useEffect } from "react";
import {
  Plus,
  Save,
  Lock,
  Unlock,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react";
import Layout from "./shared/Layout";
import Modal from "./shared/Modal";
import FormInput from "./shared/FormInput";
import Button from "./shared/Button";

const Daybook = () => {
  const [currentDaybook, setCurrentDaybook] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [transactionType, setTransactionType] = useState("جمعه"); // جمعه یا نام
  const [loading, setLoading] = useState(false);

  // د فورم ډیټا
  const [formData, setFormData] = useState({
    customer_id: "",
    customer_name: "",
    amount: "",
    currency: "افغانۍ",
    description: "",
    type: "جمعه",
  });
  const [formErrors, setFormErrors] = useState({});

  // د نمونه ډیټا
  const sampleDaybook = {
    id: 1,
    date: new Date().toLocaleDateString("fa-AF"),
    page_number: 15,
    status: "خلاص", // خلاص یا تړل شوی
    total_jamah: 125000,
    total_naam: 98000,
    balance: 27000,
  };

  const sampleTransactions = [
    {
      id: 1,
      index: 1,
      customer_name: "احمد علي خان",
      type: "جمعه",
      amount: 25000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۰:۳۰",
      balance_after: 25000,
    },
    {
      id: 2,
      index: 2,
      customer_name: "فاطمه احمد",
      type: "نام",
      amount: 15000,
      currency: "افغانۍ",
      description: "د پیسو ورکول",
      time: "۱۰:۴۵",
      balance_after: 10000,
    },
    {
      id: 3,
      index: 3,
      customer_name: "محمد حسن",
      type: "جمعه",
      amount: 50000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      time: "۱۱:۱۵",
      balance_after: 60000,
    },
  ];

  const customers = [
    { value: "1", label: "احمد علي خان" },
    { value: "2", label: "فاطمه احمد" },
    { value: "3", label: "محمد حسن" },
    { value: "4", label: "عایشه خان" },
    { value: "5", label: "علي رضا" },
  ];

  const currencies = [
    { value: "افغانۍ", label: "افغانۍ (AFN)" },
    { value: "ډالر", label: "ډالر (USD)" },
    { value: "یورو", label: "یورو (EUR)" },
    { value: "پاکستاني روپۍ", label: "پاکستاني روپۍ (PKR)" },
  ];

  useEffect(() => {
    loadDaybook();
  }, []);

  const loadDaybook = () => {
    setLoading(true);
    // د API د اړیکې لپاره وروسته به دا ځای بدل شي
    setTimeout(() => {
      setCurrentDaybook(sampleDaybook);
      setTransactions(sampleTransactions);
      setLoading(false);
    }, 1000);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // د غلطۍ پاکول
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.customer_id) {
      errors.customer_id = "د پیرودونکي انتخاب اړین دی";
    }

    if (!formData.amount || formData.amount <= 0) {
      errors.amount = "د پیسو اندازه اړینه ده";
    }

    if (!formData.description.trim()) {
      errors.description = "د راکړې ورکړې تفصیل اړین دی";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddTransaction = () => {
    if (!validateForm()) return;

    const selectedCustomer = customers.find(
      (c) => c.value === formData.customer_id
    );
    const amount = parseFloat(formData.amount);
    const lastBalance =
      transactions.length > 0
        ? transactions[transactions.length - 1].balance_after
        : 0;
    const newBalance =
      formData.type === "جمعه" ? lastBalance + amount : lastBalance - amount;

    const newTransaction = {
      id: transactions.length + 1,
      index: transactions.length + 1,
      customer_name: selectedCustomer.label,
      type: formData.type,
      amount: amount,
      currency: formData.currency,
      description: formData.description,
      time: new Date().toLocaleTimeString("fa-AF", {
        hour: "2-digit",
        minute: "2-digit",
      }),
      balance_after: newBalance,
    };

    setTransactions((prev) => [...prev, newTransaction]);

    // د ورځني کتاب د ټولو محاسبه
    const newTotalJamah =
      transactions
        .filter((t) => t.type === "جمعه")
        .reduce((sum, t) => sum + t.amount, 0) +
      (formData.type === "جمعه" ? amount : 0);

    const newTotalNaam =
      transactions
        .filter((t) => t.type === "نام")
        .reduce((sum, t) => sum + t.amount, 0) +
      (formData.type === "نام" ? amount : 0);

    setCurrentDaybook((prev) => ({
      ...prev,
      total_jamah: newTotalJamah,
      total_naam: newTotalNaam,
      balance: newTotalJamah - newTotalNaam,
    }));

    setShowAddModal(false);
    resetForm();
  };

  const resetForm = () => {
    setFormData({
      customer_id: "",
      customer_name: "",
      amount: "",
      currency: "افغانۍ",
      description: "",
      type: "جمعه",
    });
    setFormErrors({});
  };

  const openAddModal = (type) => {
    setTransactionType(type);
    setFormData((prev) => ({ ...prev, type }));
    setShowAddModal(true);
  };

  const toggleDaybookStatus = () => {
    setCurrentDaybook((prev) => ({
      ...prev,
      status: prev.status === "خلاص" ? "تړل شوی" : "خلاص",
    }));
  };

  if (loading) {
    return (
      <Layout title='ورځني کتاب (روزنامچه)'>
        <div className='flex items-center justify-center h-64'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-sarafi-600'></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title='ورځني کتاب (روزنامچه)'>
      <div className='space-y-6'>
        {/* د ورځني کتاب سرلیک */}
        <div className='bg-white rounded-lg shadow-md p-6'>
          <div className='flex items-center justify-between mb-4'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
                ورځني کتاب - صفحه {currentDaybook?.page_number}
              </h1>
              <div className='flex items-center space-x-4 mt-2'>
                <span className='text-gray-600 pashto-text'>
                  نیټه: {currentDaybook?.date}
                </span>
                <span
                  className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    currentDaybook?.status === "خلاص"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {currentDaybook?.status}
                </span>
              </div>
            </div>

            <div className='flex items-center space-x-3'>
              <Button variant='outline' icon={Calendar} onClick={() => {}}>
                نوی ورځني کتاب
              </Button>

              <Button
                variant={
                  currentDaybook?.status === "خلاص" ? "danger" : "success"
                }
                icon={currentDaybook?.status === "خلاص" ? Lock : Unlock}
                onClick={toggleDaybookStatus}
              >
                {currentDaybook?.status === "خلاص" ? "تړل" : "خلاصول"}
              </Button>
            </div>
          </div>

          {/* د ټولو لنډیز */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='bg-green-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowUpRight className='h-8 w-8 text-green-600 ml-3' />
                <div>
                  <p className='text-sm text-green-600 pashto-text'>
                    ټوله جمعه
                  </p>
                  <p className='text-xl font-bold text-green-700'>
                    {currentDaybook?.total_jamah?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-red-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <ArrowDownRight className='h-8 w-8 text-red-600 ml-3' />
                <div>
                  <p className='text-sm text-red-600 pashto-text'>ټول نام</p>
                  <p className='text-xl font-bold text-red-700'>
                    {currentDaybook?.total_naam?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-blue-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Save className='h-8 w-8 text-blue-600 ml-3' />
                <div>
                  <p className='text-sm text-blue-600 pashto-text'>میزان</p>
                  <p
                    className={`text-xl font-bold ${
                      currentDaybook?.balance >= 0
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    {currentDaybook?.balance >= 0 ? "+" : ""}
                    {currentDaybook?.balance?.toLocaleString()} افغانۍ
                  </p>
                </div>
              </div>
            </div>

            <div className='bg-gray-50 p-4 rounded-lg'>
              <div className='flex items-center'>
                <Calendar className='h-8 w-8 text-gray-600 ml-3' />
                <div>
                  <p className='text-sm text-gray-600 pashto-text'>
                    ټولې راکړې ورکړې
                  </p>
                  <p className='text-xl font-bold text-gray-700'>
                    {transactions.length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* د راکړو ورکړو برخه */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {/* د جمعې برخه */}
          <div className='bg-white rounded-lg shadow-md'>
            <div className='bg-green-50 px-6 py-4 border-b border-green-200'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-semibold text-green-800 pashto-text'>
                  جمعه (اخیستل)
                </h3>
                <Button
                  size='sm'
                  variant='success'
                  icon={Plus}
                  onClick={() => openAddModal("جمعه")}
                  disabled={currentDaybook?.status === "تړل شوی"}
                >
                  اضافه کول
                </Button>
              </div>
            </div>

            <div className='p-6'>
              <div className='space-y-3 max-h-96 overflow-y-auto'>
                {transactions
                  .filter((t) => t.type === "جمعه")
                  .map((transaction) => (
                    <div
                      key={transaction.id}
                      className='flex items-center justify-between p-3 bg-green-50 rounded-lg'
                    >
                      <div>
                        <p className='font-medium text-gray-900 pashto-text'>
                          {transaction.customer_name}
                        </p>
                        <p className='text-sm text-gray-600 pashto-text'>
                          {transaction.description}
                        </p>
                        <p className='text-xs text-gray-500'>
                          {transaction.time}
                        </p>
                      </div>
                      <div className='text-left'>
                        <p className='font-bold text-green-600'>
                          +{transaction.amount.toLocaleString()}{" "}
                          {transaction.currency}
                        </p>
                      </div>
                    </div>
                  ))}

                {transactions.filter((t) => t.type === "جمعه").length === 0 && (
                  <p className='text-center text-gray-500 py-8 pashto-text'>
                    د نن ورځې هیڅ جمعه نشته
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* د نام برخه */}
          <div className='bg-white rounded-lg shadow-md'>
            <div className='bg-red-50 px-6 py-4 border-b border-red-200'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-semibold text-red-800 pashto-text'>
                  نام (ورکول)
                </h3>
                <Button
                  size='sm'
                  variant='danger'
                  icon={Plus}
                  onClick={() => openAddModal("نام")}
                  disabled={currentDaybook?.status === "تړل شوی"}
                >
                  اضافه کول
                </Button>
              </div>
            </div>

            <div className='p-6'>
              <div className='space-y-3 max-h-96 overflow-y-auto'>
                {transactions
                  .filter((t) => t.type === "نام")
                  .map((transaction) => (
                    <div
                      key={transaction.id}
                      className='flex items-center justify-between p-3 bg-red-50 rounded-lg'
                    >
                      <div>
                        <p className='font-medium text-gray-900 pashto-text'>
                          {transaction.customer_name}
                        </p>
                        <p className='text-sm text-gray-600 pashto-text'>
                          {transaction.description}
                        </p>
                        <p className='text-xs text-gray-500'>
                          {transaction.time}
                        </p>
                      </div>
                      <div className='text-left'>
                        <p className='font-bold text-red-600'>
                          -{transaction.amount.toLocaleString()}{" "}
                          {transaction.currency}
                        </p>
                      </div>
                    </div>
                  ))}

                {transactions.filter((t) => t.type === "نام").length === 0 && (
                  <p className='text-center text-gray-500 py-8 pashto-text'>
                    د نن ورځې هیڅ نام نشته
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* د راکړې ورکړې اضافه کولو مودال */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title={`نوې ${transactionType} اضافه کړئ`}
        size='lg'
      >
        <div className='space-y-4'>
          <FormInput
            label='پیرودونکی'
            name='customer_id'
            type='select'
            value={formData.customer_id}
            onChange={handleInputChange}
            options={customers}
            placeholder='پیرودونکی انتخاب کړئ'
            required
            error={formErrors.customer_id}
          />

          <FormInput
            label='د پیسو اندازه'
            name='amount'
            type='number'
            value={formData.amount}
            onChange={handleInputChange}
            placeholder='د پیسو اندازه ولیکئ'
            required
            error={formErrors.amount}
          />

          <FormInput
            label='د پیسو ډول'
            name='currency'
            type='select'
            value={formData.currency}
            onChange={handleInputChange}
            options={currencies}
            required
          />

          <FormInput
            label='تفصیل'
            name='description'
            type='textarea'
            value={formData.description}
            onChange={handleInputChange}
            placeholder='د راکړې ورکړې تفصیل ولیکئ'
            required
            error={formErrors.description}
            rows={3}
          />

          <div className='flex justify-end space-x-3 pt-4'>
            <Button variant='secondary' onClick={() => setShowAddModal(false)}>
              لغوه کول
            </Button>
            <Button
              variant={transactionType === "جمعه" ? "success" : "danger"}
              onClick={handleAddTransaction}
            >
              ثبت کول
            </Button>
          </div>
        </div>
      </Modal>
    </Layout>
  );
};

export default Daybook;
