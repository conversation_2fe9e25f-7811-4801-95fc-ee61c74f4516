import React, { useState } from 'react';
import { Calculator, Plus, Wallet, CreditCard, PiggyBank, TrendingUp } from 'lucide-react';
import Layout from './shared/Layout';
import Button from './shared/Button';

const Accounts = () => {
  const [accounts] = useState([
    {
      id: 1,
      name: 'اصلي حساب',
      type: 'نغدي',
      balance: 1250000,
      currency: 'افغانۍ',
      status: 'فعال',
      last_transaction: '۱۴۰۲/۰۸/۱۵'
    },
    {
      id: 2,
      name: 'ډالر حساب',
      type: 'نغدي',
      balance: 5000,
      currency: 'ډالر',
      status: 'فعال',
      last_transaction: '۱۴۰۲/۰۸/۱۴'
    },
    {
      id: 3,
      name: 'بانکي حساب',
      type: 'بانکي',
      balance: 850000,
      currency: 'افغانۍ',
      status: 'فعال',
      last_transaction: '۱۴۰۲/۰۸/۱۳'
    },
    {
      id: 4,
      name: 'د پیرودونکو پورونه',
      type: 'پور',
      balance: -125000,
      currency: 'افغانۍ',
      status: 'فعال',
      last_transaction: '۱۴۰۲/۰۸/۱۲'
    }
  ]);

  const getAccountIcon = (type) => {
    switch (type) {
      case 'نغدي':
        return Wallet;
      case 'بانکي':
        return CreditCard;
      case 'پور':
        return PiggyBank;
      default:
        return Calculator;
    }
  };

  const getAccountColor = (type) => {
    switch (type) {
      case 'نغدي':
        return 'bg-green-100 text-green-600';
      case 'بانکي':
        return 'bg-blue-100 text-blue-600';
      case 'پور':
        return 'bg-red-100 text-red-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  const totalBalance = accounts
    .filter(acc => acc.currency === 'افغانۍ')
    .reduce((sum, acc) => sum + acc.balance, 0);

  const totalUSD = accounts
    .filter(acc => acc.currency === 'ډالر')
    .reduce((sum, acc) => sum + acc.balance, 0);

  return (
    <Layout title="د حساباتو مدیریت">
      <div className="space-y-6">
        {/* د لنډیز برخه */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-3">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600 pashto-text">ټول میزان (افغانۍ)</p>
                <p className={`text-2xl font-bold ${totalBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {totalBalance >= 0 ? '+' : ''}{totalBalance.toLocaleString()} افغانۍ
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-3">
                <Calculator className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600 pashto-text">ټول میزان (ډالر)</p>
                <p className={`text-2xl font-bold ${totalUSD >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {totalUSD >= 0 ? '+' : ''}${totalUSD.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-3">
                <Wallet className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600 pashto-text">فعال حسابونه</p>
                <p className="text-2xl font-bold text-purple-600">
                  {accounts.filter(acc => acc.status === 'فعال').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* د حساباتو لیست */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 pashto-text">
              د حساباتو لیست
            </h2>
            <Button variant="primary" icon={Plus}>
              نوی حساب اضافه کړئ
            </Button>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {accounts.map((account) => {
                const IconComponent = getAccountIcon(account.type);
                const colorClass = getAccountColor(account.type);
                
                return (
                  <div key={account.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ml-3 ${colorClass}`}>
                          <IconComponent className="h-6 w-6" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 pashto-text">
                            {account.name}
                          </h3>
                          <p className="text-sm text-gray-600 pashto-text">
                            {account.type}
                          </p>
                        </div>
                      </div>
                      
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        account.status === 'فعال' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {account.status}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 pashto-text">میزان:</span>
                        <span className={`text-lg font-bold ${
                          account.balance >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {account.balance >= 0 ? '+' : ''}{account.balance.toLocaleString()} {account.currency}
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 pashto-text">وروستۍ راکړه ورکړه:</span>
                        <span className="text-sm text-gray-500">
                          {account.last_transaction}
                        </span>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t border-gray-200 flex space-x-2">
                      <Button size="sm" variant="outline">
                        کتنه
                      </Button>
                      <Button size="sm" variant="outline">
                        تصحیح
                      </Button>
                      <Button size="sm" variant="outline">
                        راکړه ورکړه
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* د حساباتو تحلیل */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 pashto-text">
            د حساباتو تحلیل
          </h3>
          
          <div className="bg-gray-50 rounded-lg p-8 text-center">
            <Calculator className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2 pashto-text">
              د حساباتو تفصیلي تحلیل
            </h4>
            <p className="text-gray-600 pashto-text">
              د حساباتو د تفصیلي تحلیل لپاره دا برخه لا نه ده جوړه شوې
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Accounts;
