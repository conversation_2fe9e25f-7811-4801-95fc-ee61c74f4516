import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Users,
  BookOpen,
  DollarSign,
  TrendingUp,
  Menu,
  X,
  LogOut,
  Settings,
  FileText,
  Calculator,
  Bell,
  Search,
  Plus,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  Wallet,
  BarChart3,
} from "lucide-react";

const Dashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // د کاروونکي معلومات د localStorage څخه واخلئ
    const userData = localStorage.getItem("user");
    const token = localStorage.getItem("token");

    if (!userData || !token) {
      navigate("/login");
      return;
    }

    setUser(JSON.parse(userData));
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    navigate("/login");
  };

  // د آمار کارتونه
  const statsCards = [
    {
      title: "ټول پیرودونکي",
      value: "۱۲۳",
      icon: Users,
      color: "bg-blue-500",
      change: "+۵%",
      changeType: "positive",
      subtitle: "د تیرې میاشتې څخه",
    },
    {
      title: "د نن ورځې راکړه ورکړه",
      value: "۴۵,۰۰۰",
      currency: "افغانۍ",
      icon: DollarSign,
      color: "bg-green-500",
      change: "+۱۲%",
      changeType: "positive",
      subtitle: "د پرون څخه",
    },
    {
      title: "د میاشتې راکړه ورکړه",
      value: "۱,۲۳۴,۵۶۷",
      currency: "افغانۍ",
      icon: TrendingUp,
      color: "bg-purple-500",
      change: "+۸%",
      changeType: "positive",
      subtitle: "د تیرې میاشتې څخه",
    },
    {
      title: "د نن ورځې کتابونه",
      value: "۷",
      icon: BookOpen,
      color: "bg-orange-500",
      change: "+۳",
      changeType: "positive",
      subtitle: "نوي کتابونه",
    },

    {
      title: "د میزان توپیر",
      value: "۲۳,۴۵۶",
      currency: "افغانۍ",
      icon: Wallet,
      color: "bg-red-500",
      change: "-۱.۵%",
      changeType: "negative",
      subtitle: "د نن ورځې",
    },
  ];

  // د منیو آیټمونه
  const menuItems = [
    {
      name: "کورپاڼه",
      icon: TrendingUp,
      path: "/dashboard",
      active: true,
      badge: null,
    },
    { name: "پیرودونکي", icon: Users, path: "/customers", badge: "۱۲۳" },
    { name: "ورځني کتاب", icon: BookOpen, path: "/daybook", badge: "۷" },
    { name: "د پیرودونکي کاتا", icon: FileText, path: "/ledger", badge: null },

    { name: "راپورونه", icon: BarChart3, path: "/reports", badge: null },
    { name: "حسابات", icon: Calculator, path: "/accounts", badge: null },
    { name: "تنظیمات", icon: Settings, path: "/settings", badge: null },
  ];

  // د اخیرو فعالیتونو ډیټا
  const recentTransactions = [
    {
      id: 1,
      customerName: "احمد علي",
      type: "جمعه",
      amount: "۵,۰۰۰",
      currency: "افغانۍ",
      time: "۱۰:۳۰ صبح",
      status: "بشپړ",
    },
    {
      id: 2,
      customerName: "فاطمه خان",
      type: "نام",
      amount: "۲,۵۰۰",
      currency: "افغانۍ",
      time: "۱۰:۱۵ صبح",
      status: "بشپړ",
    },
    {
      id: 3,
      customerName: "محمد حسن",
      type: "جمعه",
      amount: "۱۰,۰۰۰",
      currency: "افغانۍ",
      time: "۹:۴۵ صبح",
      status: "بشپړ",
    },
    {
      id: 4,
      customerName: "عایشه احمد",
      type: "نام",
      amount: "۷,۵۰۰",
      currency: "افغانۍ",
      time: "۹:۳۰ صبح",
      status: "بشپړ",
    },
    {
      id: 5,
      customerName: "علي رضا",
      type: "جمعه",
      amount: "۳,۰۰۰",
      currency: "افغانۍ",
      time: "۹:۱۵ صبح",
      status: "بشپړ",
    },
  ];

  // د نویو پیرودونکو ډیټا
  const newCustomers = [
    {
      id: 1,
      name: "محمد حسن",
      phone: "۰۷۹۹۱۲۳۴۵۶",
      registrationTime: "د نن ورځې",
      status: "فعال",
    },
    {
      id: 2,
      name: "فریده احمد",
      phone: "۰۷۰۰۹۸۷۶۵۴",
      registrationTime: "د نن ورځې",
      status: "فعال",
    },
    {
      id: 3,
      name: "احمد شاه",
      phone: "۰۷۸۸۵۵۵۱۲۳",
      registrationTime: "پرون",
      status: "فعال",
    },
    {
      id: 4,
      name: "مریم خان",
      phone: "۰۷۷۷۴۴۴۳۲۱",
      registrationTime: "پرون",
      status: "فعال",
    },
    {
      id: 5,
      name: "عبدالله رحیم",
      phone: "۰۷۶۶۳۳۳۲۱۰",
      registrationTime: "۲ ورځې دمخه",
      status: "فعال",
    },
  ];

  if (!user) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-sarafi-600'></div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 flex'>
      {/* د اړخ منیو */}
      <div
        className={`fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform ${
          sidebarOpen ? "translate-x-0" : "translate-x-full"
        } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}
      >
        <div className='flex items-center justify-between h-16 px-6 border-b border-gray-200'>
          <h1 className='text-xl font-bold text-gray-900 pashto-text'>
            د صرافۍ سیسټم
          </h1>
          <button onClick={() => setSidebarOpen(false)} className='lg:hidden'>
            <X className='h-6 w-6 text-gray-500' />
          </button>
        </div>

        <nav className='mt-6'>
          {menuItems.map((item) => (
            <a
              key={item.name}
              href={item.path}
              className={`flex items-center justify-between px-6 py-3 text-sm font-medium transition-colors duration-200 ${
                item.active
                  ? "bg-sarafi-50 text-sarafi-700 border-l-4 border-sarafi-700"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              }`}
            >
              <div className='flex items-center'>
                <item.icon className='ml-3 h-5 w-5' />
                <span className='pashto-text'>{item.name}</span>
              </div>
              {item.badge && (
                <span
                  className={`px-2 py-1 text-xs font-medium rounded-full ${
                    item.badge === "نوی"
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {item.badge}
                </span>
              )}
            </a>
          ))}
        </nav>

        {/* د کاروونکي معلومات */}
        <div className='absolute bottom-0 w-full p-6 border-t border-gray-200'>
          <div className='flex items-center'>
            <div className='flex-shrink-0'>
              <div className='h-10 w-10 bg-sarafi-100 rounded-full flex items-center justify-center'>
                <span className='text-sarafi-600 font-medium'>
                  {user.full_name?.charAt(0) || "م"}
                </span>
              </div>
            </div>
            <div className='mr-3'>
              <p className='text-sm font-medium text-gray-900 pashto-text'>
                {user.full_name}
              </p>
              <p className='text-xs text-gray-500 pashto-text'>
                {user.role === "super_admin" ? "لوی مدیر" : "کاروونکی"}
              </p>
            </div>
          </div>
          <button
            onClick={handleLogout}
            className='mt-3 w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors duration-200'
          >
            <LogOut className='ml-2 h-4 w-4' />
            <span className='pashto-text'>وتل</span>
          </button>
        </div>
      </div>

      {/* اصلي مینه */}
      <div className='flex-1 lg:mr-64'>
        {/* د پورته برخه */}
        <header className='bg-white shadow-sm border-b border-gray-200'>
          <div className='flex items-center justify-between h-16 px-6'>
            <div className='flex items-center'>
              <button
                onClick={() => setSidebarOpen(true)}
                className='lg:hidden ml-4'
              >
                <Menu className='h-6 w-6 text-gray-500' />
              </button>
              <h2 className='text-2xl font-bold text-gray-900 pashto-text'>
                کورپاڼه
              </h2>
            </div>

            <div className='flex items-center space-x-4'>
              {/* د لټون برخه */}
              <div className='relative hidden md:block'>
                <div className='absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none'>
                  <Search className='h-5 w-5 text-gray-400' />
                </div>
                <input
                  type='text'
                  placeholder='د پیرودونکي لټون...'
                  className='block w-64 pr-10 pl-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-sarafi-500 focus:border-sarafi-500 text-sm'
                  dir='rtl'
                />
              </div>

              {/* د اطلاعاتو برخه */}
              <button className='relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sarafi-500'>
                <Bell className='h-6 w-6' />
                <span className='absolute top-0 left-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white'></span>
              </button>

              {/* د نیټې ښودنه */}
              <div className='flex items-center text-sm text-gray-500 pashto-text'>
                <Calendar className='h-4 w-4 ml-2' />
                <span>نن: {new Date().toLocaleDateString("fa-AF")}</span>
              </div>

              {/* د وخت ښودنه */}
              <div className='flex items-center text-sm text-gray-500 pashto-text'>
                <Clock className='h-4 w-4 ml-2' />
                <span>
                  {new Date().toLocaleTimeString("fa-AF", {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* د مینې مینه */}
        <main className='p-6'>
          {/* د ګړندیو کارونو برخه */}
          <div className='mb-8'>
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg font-semibold text-gray-900 pashto-text'>
                ګړندي کارونه
              </h3>
            </div>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
              <button className='flex flex-col items-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200'>
                <div className='w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-3'>
                  <Plus className='h-6 w-6 text-green-600' />
                </div>
                <span className='text-sm font-medium text-gray-900 pashto-text'>
                  نوې راکړه ورکړه
                </span>
              </button>

              <button className='flex flex-col items-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200'>
                <div className='w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-3'>
                  <Users className='h-6 w-6 text-blue-600' />
                </div>
                <span className='text-sm font-medium text-gray-900 pashto-text'>
                  نوی پیرودونکی
                </span>
              </button>

              <button className='flex flex-col items-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200'>
                <div className='w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-3'>
                  <Eye className='h-6 w-6 text-purple-600' />
                </div>
                <span className='text-sm font-medium text-gray-900 pashto-text'>
                  د کاتا کتنه
                </span>
              </button>

              <button className='flex flex-col items-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200'>
                <div className='w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-3'>
                  <BarChart3 className='h-6 w-6 text-orange-600' />
                </div>
                <span className='text-sm font-medium text-gray-900 pashto-text'>
                  راپور جوړول
                </span>
              </button>
            </div>
          </div>

          {/* د آمار کارتونه */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8'>
            {statsCards.map((card, index) => (
              <div
                key={index}
                className='bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200'
              >
                <div className='flex items-center justify-between'>
                  <div className='flex items-center'>
                    <div className={`${card.color} rounded-lg p-3 ml-4`}>
                      <card.icon className='h-6 w-6 text-white' />
                    </div>
                    <div>
                      <p className='text-sm font-medium text-gray-600 pashto-text mb-1'>
                        {card.title}
                      </p>
                      <div className='flex items-baseline'>
                        <p className='text-2xl font-bold text-gray-900'>
                          {card.value}
                        </p>
                        {card.currency && (
                          <span className='text-sm text-gray-500 mr-1 pashto-text'>
                            {card.currency}
                          </span>
                        )}
                      </div>
                      <div className='flex items-center mt-1'>
                        <span
                          className={`text-sm font-medium ${
                            card.changeType === "positive"
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {card.changeType === "positive" ? (
                            <ArrowUpRight className='h-4 w-4 inline ml-1' />
                          ) : (
                            <ArrowDownRight className='h-4 w-4 inline ml-1' />
                          )}
                          {card.change}
                        </span>
                        <span className='text-xs text-gray-500 mr-2 pashto-text'>
                          {card.subtitle}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* د اخیرو فعالیتونو برخه */}
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {/* د اخیرو راکړو ورکړو لیست */}
            <div className='bg-white rounded-lg shadow-md p-6'>
              <div className='flex items-center justify-between mb-4'>
                <h3 className='text-lg font-semibold text-gray-900 pashto-text'>
                  اخیرې راکړې ورکړې
                </h3>
                <button className='text-sm text-sarafi-600 hover:text-sarafi-700 pashto-text'>
                  ټول وګورئ
                </button>
              </div>
              <div className='space-y-3'>
                {recentTransactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className='flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0'
                  >
                    <div className='flex items-center'>
                      <div
                        className={`w-10 h-10 rounded-full flex items-center justify-center ml-3 ${
                          transaction.type === "جمعه"
                            ? "bg-green-100"
                            : "bg-red-100"
                        }`}
                      >
                        {transaction.type === "جمعه" ? (
                          <ArrowUpRight
                            className={`h-5 w-5 ${
                              transaction.type === "جمعه"
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          />
                        ) : (
                          <ArrowDownRight className='h-5 w-5 text-red-600' />
                        )}
                      </div>
                      <div>
                        <p className='text-sm font-medium text-gray-900 pashto-text'>
                          {transaction.customerName}
                        </p>
                        <p className='text-xs text-gray-500 pashto-text'>
                          {transaction.type} - {transaction.time}
                        </p>
                      </div>
                    </div>
                    <div className='text-left'>
                      <span
                        className={`text-sm font-medium ${
                          transaction.type === "جمعه"
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {transaction.type === "جمعه" ? "+" : "-"}
                        {transaction.amount}
                      </span>
                      <p className='text-xs text-gray-500 pashto-text'>
                        {transaction.currency}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              <div className='mt-4 pt-4 border-t border-gray-200'>
                <button className='w-full text-center text-sm text-sarafi-600 hover:text-sarafi-700 pashto-text'>
                  نوې راکړه ورکړه اضافه کړئ
                </button>
              </div>
            </div>

            {/* د اخیرو پیرودونکو لیست */}
            <div className='bg-white rounded-lg shadow-md p-6'>
              <div className='flex items-center justify-between mb-4'>
                <h3 className='text-lg font-semibold text-gray-900 pashto-text'>
                  نوي پیرودونکي
                </h3>
                <button className='text-sm text-sarafi-600 hover:text-sarafi-700 pashto-text'>
                  ټول وګورئ
                </button>
              </div>
              <div className='space-y-3'>
                {newCustomers.map((customer) => (
                  <div
                    key={customer.id}
                    className='flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0'
                  >
                    <div className='flex items-center'>
                      <div className='h-10 w-10 bg-sarafi-100 rounded-full flex items-center justify-center ml-3'>
                        <span className='text-sm font-medium text-sarafi-600'>
                          {customer.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className='text-sm font-medium text-gray-900 pashto-text'>
                          {customer.name}
                        </p>
                        <p className='text-xs text-gray-500 pashto-text'>
                          {customer.phone}
                        </p>
                      </div>
                    </div>
                    <div className='text-left'>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          customer.status === "فعال"
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        <CheckCircle className='w-3 h-3 ml-1' />
                        {customer.status}
                      </span>
                      <p className='text-xs text-gray-500 mt-1 pashto-text'>
                        {customer.registrationTime}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              <div className='mt-4 pt-4 border-t border-gray-200'>
                <button className='w-full text-center text-sm text-sarafi-600 hover:text-sarafi-700 pashto-text'>
                  نوی پیرودونکی اضافه کړئ
                </button>
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* د اړخ منیو د تیاره کولو لپاره */}
      {sidebarOpen && (
        <div
          className='fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden'
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}
    </div>
  );
};

export default Dashboard;
