import React, { useState } from 'react';
import { Settings as SettingsIcon, User, Lock, Bell, Globe, Database, Shield } from 'lucide-react';
import Layout from './shared/Layout';
import FormInput from './shared/FormInput';
import Button from './shared/Button';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [settings, setSettings] = useState({
    // د پروفایل تنظیمات
    full_name: 'د سیسټم مدیر',
    username: 'admin',
    email: '<EMAIL>',
    phone: '۰۷۹۹۱۲۳۴۵۶',
    
    // د سیسټم تنظیمات
    company_name: 'د صرافۍ دوکان',
    company_address: 'کابل، افغانستان',
    default_currency: 'افغانۍ',
    date_format: 'shamsi',
    
    // د اطلاعاتو تنظیمات
    email_notifications: true,
    sms_notifications: false,
    transaction_alerts: true,
    daily_reports: true,
    
    // د امنیت تنظیمات
    two_factor_auth: false,
    session_timeout: '30',
    password_expiry: '90'
  });

  const tabs = [
    { id: 'profile', name: 'د پروفایل تنظیمات', icon: User },
    { id: 'system', name: 'د سیسټم تنظیمات', icon: SettingsIcon },
    { id: 'notifications', name: 'د اطلاعاتو تنظیمات', icon: Bell },
    { id: 'security', name: 'د امنیت تنظیمات', icon: Shield },
    { id: 'backup', name: 'د ډیټا بیک اپ', icon: Database }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const saveSettings = () => {
    console.log('Saving settings...', settings);
    // د API د اړیکې لپاره وروسته به دا ځای بدل شي
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 pashto-text">
              د پروفایل معلومات
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormInput
                label="بشپړ نوم"
                name="full_name"
                value={settings.full_name}
                onChange={handleInputChange}
                placeholder="ستاسو بشپړ نوم ولیکئ"
              />
              
              <FormInput
                label="د کاروونکي نوم"
                name="username"
                value={settings.username}
                onChange={handleInputChange}
                placeholder="د کاروونکي نوم"
              />
              
              <FormInput
                label="بریښنالیک"
                name="email"
                type="email"
                value={settings.email}
                onChange={handleInputChange}
                placeholder="ستاسو بریښنالیک"
              />
              
              <FormInput
                label="د موبایل شمیره"
                name="phone"
                value={settings.phone}
                onChange={handleInputChange}
                placeholder="۰۷۹۹۱۲۳۴۵۶"
              />
            </div>
          </div>
        );

      case 'system':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 pashto-text">
              د سیسټم عمومي تنظیمات
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormInput
                label="د دوکان نوم"
                name="company_name"
                value={settings.company_name}
                onChange={handleInputChange}
                placeholder="د دوکان نوم ولیکئ"
              />
              
              <FormInput
                label="د دوکان پته"
                name="company_address"
                value={settings.company_address}
                onChange={handleInputChange}
                placeholder="د دوکان پته ولیکئ"
              />
              
              <FormInput
                label="اصلي پیسې"
                name="default_currency"
                type="select"
                value={settings.default_currency}
                onChange={handleInputChange}
                options={[
                  { value: 'افغانۍ', label: 'افغانۍ (AFN)' },
                  { value: 'ډالر', label: 'ډالر (USD)' },
                  { value: 'یورو', label: 'یورو (EUR)' }
                ]}
              />
              
              <FormInput
                label="د نیټې بڼه"
                name="date_format"
                type="select"
                value={settings.date_format}
                onChange={handleInputChange}
                options={[
                  { value: 'shamsi', label: 'شمسي کلیز' },
                  { value: 'hijri', label: 'قمري کلیز' },
                  { value: 'gregorian', label: 'میلادي کلیز' }
                ]}
              />
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 pashto-text">
              د اطلاعاتو تنظیمات
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 pashto-text">د بریښنالیک اطلاعات</h4>
                  <p className="text-sm text-gray-600 pashto-text">د مهمو پیښو لپاره بریښنالیک واستوئ</p>
                </div>
                <input
                  type="checkbox"
                  name="email_notifications"
                  checked={settings.email_notifications}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded"
                />
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 pashto-text">د SMS اطلاعات</h4>
                  <p className="text-sm text-gray-600 pashto-text">د مهمو پیښو لپاره SMS واستوئ</p>
                </div>
                <input
                  type="checkbox"
                  name="sms_notifications"
                  checked={settings.sms_notifications}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded"
                />
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 pashto-text">د راکړو ورکړو خبرداری</h4>
                  <p className="text-sm text-gray-600 pashto-text">د نویو راکړو ورکړو لپاره خبرداری</p>
                </div>
                <input
                  type="checkbox"
                  name="transaction_alerts"
                  checked={settings.transaction_alerts}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded"
                />
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 pashto-text">ورځني راپورونه</h4>
                  <p className="text-sm text-gray-600 pashto-text">د هرې ورځې د پای راپور واستوئ</p>
                </div>
                <input
                  type="checkbox"
                  name="daily_reports"
                  checked={settings.daily_reports}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded"
                />
              </div>
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 pashto-text">
              د امنیت تنظیمات
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 pashto-text">دوه مرحلیز تصدیق</h4>
                  <p className="text-sm text-gray-600 pashto-text">د اضافي امنیت لپاره دوه مرحلیز تصدیق فعال کړئ</p>
                </div>
                <input
                  type="checkbox"
                  name="two_factor_auth"
                  checked={settings.two_factor_auth}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormInput
                  label="د ناستې د ختمیدو وخت (دقیقې)"
                  name="session_timeout"
                  type="number"
                  value={settings.session_timeout}
                  onChange={handleInputChange}
                  placeholder="30"
                />
                
                <FormInput
                  label="د پاسورډ د ختمیدو وخت (ورځې)"
                  name="password_expiry"
                  type="number"
                  value={settings.password_expiry}
                  onChange={handleInputChange}
                  placeholder="90"
                />
              </div>
              
              <div className="pt-4">
                <Button variant="outline" icon={Lock}>
                  پاسورډ بدلول
                </Button>
              </div>
            </div>
          </div>
        );

      case 'backup':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 pashto-text">
              د ډیټا بیک اپ او بیرته راوړل
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <Database className="h-8 w-8 text-blue-600 ml-3" />
                  <div>
                    <h4 className="font-medium text-gray-900 pashto-text">د ډیټا بیک اپ</h4>
                    <p className="text-sm text-gray-600 pashto-text">د ټولو معلوماتو بیک اپ واخلئ</p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <p className="text-sm text-gray-600 pashto-text">
                    وروستی بیک اپ: ۱۴۰۲/۰۸/۱۵
                  </p>
                  <Button variant="primary" className="w-full">
                    اوس بیک اپ واخلئ
                  </Button>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <Globe className="h-8 w-8 text-green-600 ml-3" />
                  <div>
                    <h4 className="font-medium text-gray-900 pashto-text">د ډیټا بیرته راوړل</h4>
                    <p className="text-sm text-gray-600 pashto-text">د پخوانیو معلوماتو بیرته راوړل</p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <input
                    type="file"
                    accept=".sql,.json"
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-sarafi-50 file:text-sarafi-700 hover:file:bg-sarafi-100"
                  />
                  <Button variant="success" className="w-full">
                    بیرته راوړل
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <Shield className="h-5 w-5 text-yellow-400" />
                </div>
                <div className="mr-3">
                  <h3 className="text-sm font-medium text-yellow-800 pashto-text">
                    مهم خبرداری
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700 pashto-text">
                    <p>
                      د ډیټا د بیرته راوړلو دمخه ډاډ ترلاسه کړئ چې ستاسو د اوسنیو معلوماتو بیک اپ شتون لري.
                      دا عمل ستاسو ټول اوسني معلومات له منځه وړي.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Layout title="د سیسټم تنظیمات">
      <div className="space-y-6">
        {/* د ټابونو منیو */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-sarafi-500 text-sarafi-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                  >
                    <IconComponent className="h-5 w-5 ml-2" />
                    <span className="pashto-text">{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
          
          {/* د ټاب مینه */}
          <div className="p-6">
            {renderTabContent()}
          </div>
          
          {/* د ثبت کولو تڼۍ */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
            <Button variant="primary" onClick={saveSettings}>
              تنظیمات ثبت کړئ
            </Button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Settings;
