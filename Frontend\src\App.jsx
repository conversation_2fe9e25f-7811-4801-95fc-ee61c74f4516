import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import Login from "./components/Login";
import Dashboard from "./components/Dashboard";
import Customers from "./components/Customers";
import Daybook from "./components/Daybook";
import Ledger from "./components/Ledger";
import Reports from "./components/Reports";
import Accounts from "./components/Accounts";
import Settings from "./components/Settings";

// د محافظت شوي روټ کمپوننټ
const ProtectedRoute = ({ children }) => {
  const token = localStorage.getItem("token");
  return token ? children : <Navigate to='/login' replace />;
};

function App() {
  return (
    <Router>
      <div className='App'>
        <Routes>
          <Route path='/login' element={<Login />} />
          <Route
            path='/dashboard'
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path='/'
            element={
              <ProtectedRoute>
                <Navigate to='/dashboard' replace />
              </ProtectedRoute>
            }
          />
          {/* د نورو صفحو لپاره روټونه دلته اضافه کړئ */}
          <Route
            path='/customers'
            element={
              <ProtectedRoute>
                <Customers />
              </ProtectedRoute>
            }
          />
          <Route
            path='/daybook'
            element={
              <ProtectedRoute>
                <Daybook />
              </ProtectedRoute>
            }
          />
          <Route
            path='/ledger'
            element={
              <ProtectedRoute>
                <Ledger />
              </ProtectedRoute>
            }
          />

          <Route
            path='/reports'
            element={
              <ProtectedRoute>
                <Reports />
              </ProtectedRoute>
            }
          />
          <Route
            path='/accounts'
            element={
              <ProtectedRoute>
                <Accounts />
              </ProtectedRoute>
            }
          />
          <Route
            path='/settings'
            element={
              <ProtectedRoute>
                <Settings />
              </ProtectedRoute>
            }
          />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
