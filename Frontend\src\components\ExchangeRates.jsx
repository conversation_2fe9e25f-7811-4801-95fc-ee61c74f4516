import React, { useState, useEffect } from 'react';
import { Plus, Edit, TrendingUp, TrendingDown, RefreshCw, Clock, Banknote } from 'lucide-react';
import Layout from './shared/Layout';
import Modal from './shared/Modal';
import FormInput from './shared/FormInput';
import Button from './shared/Button';

const ExchangeRates = () => {
  const [rates, setRates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedRate, setSelectedRate] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  
  // د فورم ډیټا
  const [formData, setFormData] = useState({
    currency_code: '',
    currency_name: '',
    buy_rate: '',
    sell_rate: '',
    is_active: true
  });
  const [formErrors, setFormErrors] = useState({});

  // د نمونه ډیټا
  const sampleRates = [
    {
      id: 1,
      currency_code: 'USD',
      currency_name: 'امریکایي ډالر',
      buy_rate: 70.50,
      sell_rate: 71.00,
      previous_buy: 70.30,
      previous_sell: 70.80,
      change_percentage: 0.28,
      is_active: true,
      last_updated: new Date().toLocaleString('fa-AF')
    },
    {
      id: 2,
      currency_code: 'EUR',
      currency_name: 'یورو',
      buy_rate: 76.20,
      sell_rate: 76.80,
      previous_buy: 76.00,
      previous_sell: 76.60,
      change_percentage: 0.26,
      is_active: true,
      last_updated: new Date().toLocaleString('fa-AF')
    },
    {
      id: 3,
      currency_code: 'PKR',
      currency_name: 'پاکستاني روپۍ',
      buy_rate: 0.25,
      sell_rate: 0.26,
      previous_buy: 0.26,
      previous_sell: 0.27,
      change_percentage: -3.85,
      is_active: true,
      last_updated: new Date().toLocaleString('fa-AF')
    },
    {
      id: 4,
      currency_code: 'IRR',
      currency_name: 'ایراني تومان',
      buy_rate: 0.0017,
      sell_rate: 0.0018,
      previous_buy: 0.0017,
      previous_sell: 0.0018,
      change_percentage: 0.00,
      is_active: true,
      last_updated: new Date().toLocaleString('fa-AF')
    },
    {
      id: 5,
      currency_code: 'GBP',
      currency_name: 'برتانوي پونډ',
      buy_rate: 89.50,
      sell_rate: 90.20,
      previous_buy: 89.80,
      previous_sell: 90.50,
      change_percentage: -0.33,
      is_active: false,
      last_updated: new Date().toLocaleString('fa-AF')
    }
  ];

  const currencyOptions = [
    { value: 'USD', label: 'USD - امریکایي ډالر' },
    { value: 'EUR', label: 'EUR - یورو' },
    { value: 'GBP', label: 'GBP - برتانوي پونډ' },
    { value: 'PKR', label: 'PKR - پاکستاني روپۍ' },
    { value: 'IRR', label: 'IRR - ایراني تومان' },
    { value: 'SAR', label: 'SAR - سعودي ریال' },
    { value: 'AED', label: 'AED - اماراتي درهم' }
  ];

  useEffect(() => {
    loadExchangeRates();
  }, []);

  const loadExchangeRates = () => {
    setLoading(true);
    // د API د اړیکې لپاره وروسته به دا ځای بدل شي
    setTimeout(() => {
      setRates(sampleRates);
      setLastUpdated(new Date());
      setLoading(false);
    }, 1000);
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // د غلطۍ پاکول
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.currency_code) {
      errors.currency_code = 'د پیسو کوډ اړین دی';
    }
    
    if (!formData.currency_name.trim()) {
      errors.currency_name = 'د پیسو نوم اړین دی';
    }
    
    if (!formData.buy_rate || formData.buy_rate <= 0) {
      errors.buy_rate = 'د پیرودنې نرخ اړین دی';
    }
    
    if (!formData.sell_rate || formData.sell_rate <= 0) {
      errors.sell_rate = 'د پلورنې نرخ اړین دی';
    }
    
    if (parseFloat(formData.sell_rate) <= parseFloat(formData.buy_rate)) {
      errors.sell_rate = 'د پلورنې نرخ باید د پیرودنې نرخ څخه زیات وي';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddRate = () => {
    if (!validateForm()) return;
    
    const newRate = {
      id: rates.length + 1,
      currency_code: formData.currency_code,
      currency_name: formData.currency_name,
      buy_rate: parseFloat(formData.buy_rate),
      sell_rate: parseFloat(formData.sell_rate),
      previous_buy: parseFloat(formData.buy_rate),
      previous_sell: parseFloat(formData.sell_rate),
      change_percentage: 0,
      is_active: formData.is_active,
      last_updated: new Date().toLocaleString('fa-AF')
    };
    
    setRates(prev => [...prev, newRate]);
    setShowAddModal(false);
    resetForm();
  };

  const handleEditRate = () => {
    if (!validateForm()) return;
    
    setRates(prev => prev.map(rate =>
      rate.id === selectedRate.id
        ? {
            ...rate,
            currency_name: formData.currency_name,
            previous_buy: rate.buy_rate,
            previous_sell: rate.sell_rate,
            buy_rate: parseFloat(formData.buy_rate),
            sell_rate: parseFloat(formData.sell_rate),
            change_percentage: ((parseFloat(formData.buy_rate) - rate.buy_rate) / rate.buy_rate * 100),
            is_active: formData.is_active,
            last_updated: new Date().toLocaleString('fa-AF')
          }
        : rate
    ));
    
    setShowEditModal(false);
    resetForm();
  };

  const resetForm = () => {
    setFormData({
      currency_code: '',
      currency_name: '',
      buy_rate: '',
      sell_rate: '',
      is_active: true
    });
    setFormErrors({});
    setSelectedRate(null);
  };

  const openEditModal = (rate) => {
    setSelectedRate(rate);
    setFormData({
      currency_code: rate.currency_code,
      currency_name: rate.currency_name,
      buy_rate: rate.buy_rate.toString(),
      sell_rate: rate.sell_rate.toString(),
      is_active: rate.is_active
    });
    setShowEditModal(true);
  };

  const toggleRateStatus = (rateId) => {
    setRates(prev => prev.map(rate =>
      rate.id === rateId
        ? { ...rate, is_active: !rate.is_active }
        : rate
    ));
  };

  const refreshRates = () => {
    loadExchangeRates();
  };

  return (
    <Layout title="د اسعارو مدیریت">
      <div className="space-y-6">
        {/* د پورته برخه */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 pashto-text">د اسعارو لیست</h1>
            <div className="flex items-center mt-2 text-sm text-gray-500">
              <Clock className="h-4 w-4 ml-2" />
              <span className="pashto-text">وروستی تازه کول: {lastUpdated.toLocaleString('fa-AF')}</span>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              icon={RefreshCw}
              onClick={refreshRates}
              loading={loading}
            >
              تازه کول
            </Button>
            <Button
              onClick={() => setShowAddModal(true)}
              icon={Plus}
              variant="primary"
            >
              نوی نرخ اضافه کړئ
            </Button>
          </div>
        </div>

        {/* د اسعارو کارتونه */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {rates.map((rate) => (
            <div
              key={rate.id}
              className={`bg-white rounded-lg shadow-md p-6 border-l-4 ${
                rate.is_active ? 'border-green-500' : 'border-gray-400'
              }`}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-sarafi-100 rounded-lg flex items-center justify-center ml-3">
                    <Banknote className="h-6 w-6 text-sarafi-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">{rate.currency_code}</h3>
                    <p className="text-sm text-gray-600 pashto-text">{rate.currency_name}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => openEditModal(rate)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  
                  <button
                    onClick={() => toggleRateStatus(rate.id)}
                    className={`px-2 py-1 text-xs font-medium rounded-full ${
                      rate.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {rate.is_active ? 'فعال' : 'غیر فعال'}
                  </button>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 pashto-text">د پیرودنې نرخ:</span>
                  <span className="font-bold text-green-600">
                    {rate.buy_rate.toFixed(2)} افغانۍ
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 pashto-text">د پلورنې نرخ:</span>
                  <span className="font-bold text-blue-600">
                    {rate.sell_rate.toFixed(2)} افغانۍ
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 pashto-text">بدلون:</span>
                  <div className="flex items-center">
                    {rate.change_percentage > 0 ? (
                      <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    ) : rate.change_percentage < 0 ? (
                      <TrendingDown className="h-4 w-4 text-red-500 ml-1" />
                    ) : null}
                    <span className={`text-sm font-medium ${
                      rate.change_percentage > 0 ? 'text-green-600' :
                      rate.change_percentage < 0 ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {rate.change_percentage > 0 ? '+' : ''}{rate.change_percentage.toFixed(2)}%
                    </span>
                  </div>
                </div>
                
                <div className="pt-2 border-t border-gray-200">
                  <p className="text-xs text-gray-500 pashto-text">
                    وروستی تازه کول: {rate.last_updated}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sarafi-600"></div>
            <span className="mr-3 text-gray-600 pashto-text">د اسعارو بارول...</span>
          </div>
        )}
      </div>

      {/* د اضافه کولو مودال */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="نوی نرخ اضافه کړئ"
        size="lg"
      >
        <div className="space-y-4">
          <FormInput
            label="د پیسو کوډ"
            name="currency_code"
            type="select"
            value={formData.currency_code}
            onChange={handleInputChange}
            options={currencyOptions}
            placeholder="د پیسو کوډ انتخاب کړئ"
            required
            error={formErrors.currency_code}
          />
          
          <FormInput
            label="د پیسو نوم"
            name="currency_name"
            value={formData.currency_name}
            onChange={handleInputChange}
            placeholder="د پیسو نوم ولیکئ"
            required
            error={formErrors.currency_name}
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              label="د پیرودنې نرخ (افغانۍ)"
              name="buy_rate"
              type="number"
              value={formData.buy_rate}
              onChange={handleInputChange}
              placeholder="70.50"
              required
              error={formErrors.buy_rate}
            />
            
            <FormInput
              label="د پلورنې نرخ (افغانۍ)"
              name="sell_rate"
              type="number"
              value={formData.sell_rate}
              onChange={handleInputChange}
              placeholder="71.00"
              required
              error={formErrors.sell_rate}
            />
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              name="is_active"
              checked={formData.is_active}
              onChange={handleInputChange}
              className="h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="mr-2 text-sm text-gray-700 pashto-text">
              فعال نرخ
            </label>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => setShowAddModal(false)}
            >
              لغوه کول
            </Button>
            <Button
              variant="primary"
              onClick={handleAddRate}
            >
              ثبت کول
            </Button>
          </div>
        </div>
      </Modal>

      {/* د تصحیح مودال */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="د نرخ تصحیح"
        size="lg"
      >
        <div className="space-y-4">
          <FormInput
            label="د پیسو کوډ"
            name="currency_code"
            value={formData.currency_code}
            disabled={true}
            className="bg-gray-100"
          />
          
          <FormInput
            label="د پیسو نوم"
            name="currency_name"
            value={formData.currency_name}
            onChange={handleInputChange}
            placeholder="د پیسو نوم ولیکئ"
            required
            error={formErrors.currency_name}
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              label="د پیرودنې نرخ (افغانۍ)"
              name="buy_rate"
              type="number"
              value={formData.buy_rate}
              onChange={handleInputChange}
              placeholder="70.50"
              required
              error={formErrors.buy_rate}
            />
            
            <FormInput
              label="د پلورنې نرخ (افغانۍ)"
              name="sell_rate"
              type="number"
              value={formData.sell_rate}
              onChange={handleInputChange}
              placeholder="71.00"
              required
              error={formErrors.sell_rate}
            />
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="edit_is_active"
              name="is_active"
              checked={formData.is_active}
              onChange={handleInputChange}
              className="h-4 w-4 text-sarafi-600 focus:ring-sarafi-500 border-gray-300 rounded"
            />
            <label htmlFor="edit_is_active" className="mr-2 text-sm text-gray-700 pashto-text">
              فعال نرخ
            </label>
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => setShowEditModal(false)}
            >
              لغوه کول
            </Button>
            <Button
              variant="primary"
              onClick={handleEditRate}
            >
              تصحیح کول
            </Button>
          </div>
        </div>
      </Modal>
    </Layout>
  );
};

export default ExchangeRates;
